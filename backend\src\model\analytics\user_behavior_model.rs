use chrono::{DateTime, NaiveDate, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use std::collections::HashMap;
use uuid::Uuid;

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct UserBehaviorLog {
    pub id: Uuid,
    pub user_id: Option<Uuid>,
    pub tenant_id: Option<Uuid>,
    pub session_id: Uuid,
    
    // 行为信息
    pub action_type: String,
    pub module: String,
    pub page_path: Option<String>,
    pub element: Option<String>,
    
    // 上下文信息
    pub resource_type: Option<String>,
    pub resource_id: Option<Uuid>,
    pub resource_name: Option<String>,
    
    // 请求信息
    pub request_method: Option<String>,
    pub request_url: Option<String>,
    pub request_params: Option<serde_json::Value>,
    pub response_status: Option<i32>,
    pub response_time_ms: Option<i32>,
    
    // 环境信息
    pub ip_address: Option<std::net::IpAddr>,
    pub user_agent: Option<String>,
    pub device_type: Option<String>,
    pub browser: Option<String>,
    pub os: Option<String>,
    pub screen_resolution: Option<String>,
    
    // 业务信息
    pub duration_ms: Option<i32>,
    pub success: bool,
    pub error_message: Option<String>,
    pub additional_data: Option<serde_json::Value>,
    
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct UserBehaviorSummary {
    pub id: Uuid,
    pub user_id: Uuid,
    pub tenant_id: Option<Uuid>,
    pub date: NaiveDate,
    
    // 活动统计
    pub login_count: Option<i32>,
    pub page_views: Option<i32>,
    pub total_actions: Option<i32>,
    pub session_count: Option<i32>,
    pub total_duration_seconds: Option<i32>,
    pub avg_session_duration_seconds: Option<i32>,
    
    // 模块使用统计
    pub module_stats: Option<serde_json::Value>,
    
    // 设备统计
    pub device_stats: Option<serde_json::Value>,
    
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct SystemUsageStats {
    pub id: Uuid,
    pub tenant_id: Option<Uuid>,
    pub stat_date: NaiveDate,
    pub hour_of_day: Option<i32>,
    
    // 用户统计
    pub unique_users: Option<i32>,
    pub total_sessions: Option<i32>,
    pub new_users: Option<i32>,
    pub returning_users: Option<i32>,
    
    // 活动统计
    pub total_page_views: Option<i32>,
    pub total_actions: Option<i32>,
    pub avg_session_duration_seconds: Option<i32>,
    
    // 热门功能
    pub top_modules: Option<serde_json::Value>,
    pub top_pages: Option<serde_json::Value>,
    
    // 设备统计
    pub device_breakdown: Option<serde_json::Value>,
    pub browser_breakdown: Option<serde_json::Value>,
    
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

// 创建用户行为日志的请求结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateBehaviorLogRequest {
    pub user_id: Option<Uuid>,
    pub tenant_id: Option<Uuid>,
    pub session_id: Uuid,
    
    pub action_type: String,
    pub module: String,
    pub page_path: Option<String>,
    pub element: Option<String>,
    
    pub resource_type: Option<String>,
    pub resource_id: Option<Uuid>,
    pub resource_name: Option<String>,
    
    pub request_method: Option<String>,
    pub request_url: Option<String>,
    pub request_params: Option<serde_json::Value>,
    pub response_status: Option<i32>,
    pub response_time_ms: Option<i32>,
    
    pub ip_address: Option<String>,
    pub user_agent: Option<String>,
    pub device_type: Option<String>,
    pub browser: Option<String>,
    pub os: Option<String>,
    pub screen_resolution: Option<String>,
    
    pub duration_ms: Option<i32>,
    pub success: Option<bool>,
    pub error_message: Option<String>,
    pub additional_data: Option<serde_json::Value>,
}

// 用户行为查询参数
#[derive(Debug, Clone, Deserialize, Default)]
pub struct BehaviorLogQueryParams {
    pub user_id: Option<Uuid>,
    pub tenant_id: Option<Uuid>,
    pub session_id: Option<Uuid>,
    pub action_type: Option<String>,
    pub module: Option<String>,
    pub start_date: Option<DateTime<Utc>>,
    pub end_date: Option<DateTime<Utc>>,
    pub success: Option<bool>,
    pub page: Option<i32>,
    pub page_size: Option<i32>,
}

// 行为分析的响应数据结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BehaviorAnalyticsData {
    pub total_actions: i64,
    pub unique_users: i64,
    pub unique_sessions: i64,
    pub avg_session_duration: f64,
    pub most_active_modules: Vec<ModuleActivity>,
    pub hourly_distribution: Vec<HourlyActivity>,
    pub device_distribution: HashMap<String, i64>,
    pub top_pages: Vec<PageActivity>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModuleActivity {
    pub module: String,
    pub count: i64,
    pub unique_users: i64,
    pub avg_duration_seconds: Option<f64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HourlyActivity {
    pub hour: i32,
    pub actions: i64,
    pub unique_users: i64,
    pub avg_response_time_ms: Option<f64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PageActivity {
    pub page_path: String,
    pub views: i64,
    pub unique_users: i64,
    pub avg_duration_seconds: Option<f64>,
}

// 用户行为趋势数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserBehaviorTrend {
    pub date: NaiveDate,
    pub total_actions: i64,
    pub unique_users: i64,
    pub avg_session_duration: f64,
    pub login_count: i64,
}

// 实时用户行为指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RealTimeBehaviorMetrics {
    pub current_online_users: i64,
    pub actions_last_hour: i64,
    pub actions_today: i64,
    pub avg_response_time_ms: f64,
    pub error_rate_percent: f64,
    pub top_active_modules: Vec<ModuleActivity>,
} 