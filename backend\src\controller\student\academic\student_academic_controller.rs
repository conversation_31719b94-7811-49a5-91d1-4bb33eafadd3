use axum::{
    extract::{Path, Query, State},
    routing::get,
    Router,
};
use uuid::Uuid;

use crate::middleware::auth_middleware::AuthExtractor;
use crate::middleware::tenant_middleware::TenantExtractor;
use crate::model::analytics::student_academic::StudentAcademicQuery;
use crate::utils::api_response::{responses, ApiResponse};
use crate::web_server::AppState;

/// GET /api/v1/students/{student_id}/scores
/// 获取学生考试成绩列表
pub async fn get_student_scores(
    State(state): State<AppState>,
    Path(student_id): Path<Uuid>,
    Query(query_params): Query<StudentAcademicQuery>,
    AuthExtractor(_user): AuthExtractor,
    TenantExtractor(tenant_context): TenantExtractor,
) -> Result<ApiResponse<Vec<crate::model::analytics::student_academic::StudentExamScore>>, ApiResponse<()>> {
    state.student_academic_service.get_exam_scores(
        &tenant_context.schema_name,
        student_id,
        &query_params,
    ).await
    .map_err(|e| responses::error(&e.to_string(), None))
    .map(|data| responses::success(data, None))
}

/// GET /api/v1/students/{student_id}/performance
/// 获取学生学科表现分析
pub async fn get_student_performance(
    State(state): State<AppState>,
    Path(student_id): Path<Uuid>,
    Query(query_params): Query<StudentAcademicQuery>,
    AuthExtractor(_user): AuthExtractor,
    TenantExtractor(tenant_context): TenantExtractor,
) -> Result<ApiResponse<Vec<crate::model::analytics::student_academic::ScoreTrendData>>, ApiResponse<()>> {
    let date_range = query_params.date_range.as_deref().unwrap_or("90d");
    state.student_academic_service.get_subject_performance(
        &tenant_context.schema_name,
        student_id,
        date_range,
    ).await
    .map_err(|e| responses::error(&e.to_string(), None))
    .map(|data| responses::success(data, None))
}

/// GET /api/v1/students/{student_id}/homework
/// 获取学生作业状态
pub async fn get_student_homework(
    State(state): State<AppState>,
    Path(student_id): Path<Uuid>,
    Query(query_params): Query<StudentAcademicQuery>,
    AuthExtractor(_user): AuthExtractor,
    TenantExtractor(tenant_context): TenantExtractor,
) -> Result<ApiResponse<Vec<crate::model::analytics::student_academic::StudentHomeworkRecord>>, ApiResponse<()>> {
    state.student_academic_service.get_homework_status(
        &tenant_context.schema_name,
        student_id,
        &query_params,
    ).await
    .map_err(|e| responses::error(&e.to_string(), None))
    .map(|data| responses::success(data, None))
}

/// GET /api/v1/students/{student_id}/academic-overview
/// 获取学生学术表现综合概览
pub async fn get_student_academic_overview(
    State(state): State<AppState>,
    Path(student_id): Path<Uuid>,
    AuthExtractor(_user): AuthExtractor,
    TenantExtractor(tenant_context): TenantExtractor,
) -> Result<ApiResponse<crate::model::analytics::student_academic::StudentAcademicOverview>, ApiResponse<()>> {
    state.student_academic_service.get_academic_overview(
        &tenant_context.schema_name,
        student_id,
    ).await
    .map_err(|e| responses::error(&e.to_string(), None))
    .map(|data| responses::success(data, None))
}

/// GET /api/v1/profile/scores
/// 获取当前登录学生的考试成绩列表
pub async fn get_student_self_scores(
    State(state): State<AppState>,
    Query(query_params): Query<StudentAcademicQuery>,
    AuthExtractor(user): AuthExtractor,
    TenantExtractor(tenant_context): TenantExtractor,
) -> Result<ApiResponse<Vec<crate::model::analytics::student_academic::StudentExamScore>>, ApiResponse<()>> {
    state.student_academic_service.get_exam_scores(
        &tenant_context.schema_name,
        user.user_id,
        &query_params,
    ).await
    .map_err(|e| responses::error(&e.to_string(), None))
    .map(|data| responses::success(data, None))
}

/// GET /api/v1/profile/performance
/// 获取当前登录学生的学科表现分析
pub async fn get_student_self_performance(
    State(state): State<AppState>,
    Query(query_params): Query<StudentAcademicQuery>,
    AuthExtractor(user): AuthExtractor,
    TenantExtractor(tenant_context): TenantExtractor,
) -> Result<ApiResponse<Vec<crate::model::analytics::student_academic::ScoreTrendData>>, ApiResponse<()>> {
    let date_range = query_params.date_range.as_deref().unwrap_or("90d");
    state.student_academic_service.get_subject_performance(
        &tenant_context.schema_name,
        user.user_id,
        date_range,
    ).await
    .map_err(|e| responses::error(&e.to_string(), None))
    .map(|data| responses::success(data, None))
}

/// GET /api/v1/profile/homework
/// 获取当前登录学生的作业状态
pub async fn get_student_self_homework(
    State(state): State<AppState>,
    Query(query_params): Query<StudentAcademicQuery>,
    AuthExtractor(user): AuthExtractor,
    TenantExtractor(tenant_context): TenantExtractor,
) -> Result<ApiResponse<Vec<crate::model::analytics::student_academic::StudentHomeworkRecord>>, ApiResponse<()>> {
    state.student_academic_service.get_homework_status(
        &tenant_context.schema_name,
        user.user_id,
        &query_params,
    ).await
    .map_err(|e| responses::error(&e.to_string(), None))
    .map(|data| responses::success(data, None))
}

/// GET /api/v1/profile/academic-overview
/// 获取当前登录学生的学术表现综合概览
pub async fn get_student_self_academic_overview(
    State(state): State<AppState>,
    AuthExtractor(user): AuthExtractor,
    TenantExtractor(tenant_context): TenantExtractor,
) -> Result<ApiResponse<crate::model::analytics::student_academic::StudentAcademicOverview>, ApiResponse<()>> {
    state.student_academic_service.get_academic_overview(
        &tenant_context.schema_name,
        user.user_id,
    ).await
    .map_err(|e| responses::error(&e.to_string(), None))
    .map(|data| responses::success(data, None))
}

/// 创建学生学术数据相关的路由
pub fn create_router() -> Router<AppState> {
    Router::new()
        // 管理员/教师查看指定学生的学术数据
        .route("/{student_id}/scores", get(get_student_scores))
        .route("/{student_id}/performance", get(get_student_performance))
        .route("/{student_id}/homework", get(get_student_homework))
        .route("/{student_id}/academic-overview", get(get_student_academic_overview))
}

/// 创建当前登录学生的学术数据路由（兼容接口）
pub fn create_student_self_router() -> Router<AppState> {
    Router::new()
        // 学生查看自己的学术数据
        .route("/scores", get(get_student_self_scores))
        .route("/performance", get(get_student_self_performance))
        .route("/homework", get(get_student_self_homework))
        .route("/academic-overview", get(get_student_self_academic_overview))
}
