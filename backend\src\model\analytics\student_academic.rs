use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;

/// 学生学术表现概览
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct StudentAcademicOverview {
    pub student_id: Uuid,
    pub total_exams: i64,
    pub average_score: f64,
    pub class_rank: i32,
    pub grade_rank: i32,
    pub improvement_trend: String, // 'up', 'down', 'stable'
    pub improvement_rate: f64,
    pub homework_completion_rate: f64,
}

/// 学生考试成绩记录
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct StudentExamScore {
    pub id: Uuid,
    pub student_id: Uuid,
    pub exam_id: Uuid,
    pub subject_id: Uuid,
    pub subject_name: String,
    pub score: f32,
    pub total_possible: f32,
    pub percentage: f32,
    pub rank: i32,
    pub exam_date: DateTime<Utc>,
    pub exam_name: String,
}

/// 学科表现趋势数据点
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct SubjectPerformanceDataPoint {
    pub date: String,
    pub score: f32,
    pub percentage: f32,
}

/// 学生学科表现分析
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StudentSubjectPerformance {
    pub subject: String,
    pub subject_id: Uuid,
    pub latest_score: f32,
    pub trend: String, // 'up', 'down', 'stable'
    pub data: Vec<SubjectPerformanceDataPoint>,
}

/// 学生作业完成记录
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct StudentHomeworkRecord {
    pub id: Uuid,
    pub homework_id: Uuid,
    pub student_id: Uuid,
    pub homework_name: String,
    pub subject_name: String,
    pub due_date: DateTime<Utc>,
    pub status: String, // 'completed', 'pending', 'overdue'
    pub score: Option<f32>,
    pub total_score: Option<f32>,
    pub submitted_at: Option<DateTime<Utc>>,
}

/// 学生学术数据查询参数
#[derive(Debug, Deserialize)]
pub struct StudentAcademicQuery {
    pub limit: Option<i32>,
    pub subject_id: Option<Uuid>,
    pub date_range: Option<String>, // '30d', '90d', '180d', '1y'
    pub exam_type: Option<String>,
}

/// 学生成绩统计响应
#[derive(Debug, Serialize)]
pub struct StudentScoreStatistics {
    pub overview: StudentAcademicOverview,
    pub recent_scores: Vec<StudentExamScore>,
}

/// 学生表现分析响应
#[derive(Debug, Serialize)]
pub struct StudentPerformanceAnalysis {
    pub subject_performance: Vec<StudentSubjectPerformance>,
    pub improvement_suggestions: Vec<String>,
}

/// 学生作业状态响应
#[derive(Debug, Serialize)]
pub struct StudentHomeworkStatus {
    pub homework_records: Vec<StudentHomeworkRecord>,
    pub completion_rate: f32,
    pub pending_count: i32,
    pub overdue_count: i32,
}

/// 成绩趋势计算辅助结构
#[derive(Debug, FromRow, Serialize, Deserialize)]
pub struct ScoreTrendData {
    pub subject_id: Uuid,
    pub subject_name: String,
    pub exam_date: DateTime<Utc>,
    pub score: f32,
    pub total_possible: f32,
}

/// 排名计算辅助结构
#[derive(Debug, FromRow)]
pub struct RankingData {
    pub student_id: Uuid,
    pub average_score: f32,
    pub class_id: Uuid,
    pub grade_level: String,
}
