use anyhow::Result;
use sqlx::PgPool;
use uuid::Uuid;

use crate::model::analytics::student_performance::{
    KnowledgePointMastery, SubjectAbility, ProgressTrack, StudyRecommendation,
    StudentPerformanceQuery
};
use crate::repository::analytics::student_performance_repository::StudentPerformanceRepository;

pub struct StudentPerformanceService {
    pool: PgPool,
}

impl StudentPerformanceService {
    /// 创建新的StudentPerformanceService实例
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    /// 获取学生知识点掌握情况
    pub async fn get_knowledge_point_mastery(
        &self,
        tenant_schema: &str,
        student_id: Uuid,
        query: &StudentPerformanceQuery,
    ) -> Result<Vec<KnowledgePointMastery>> {
        StudentPerformanceRepository::get_knowledge_point_mastery(
            &self.pool,
            tenant_schema,
            student_id,
            query,
        ).await
    }

    /// 获取学科能力雷达图数据
    pub async fn get_subject_abilities(
        &self,
        tenant_schema: &str,
        student_id: Uuid,
        query: &StudentPerformanceQuery,
    ) -> Result<Vec<SubjectAbility>> {
        StudentPerformanceRepository::get_subject_abilities(
            &self.pool,
            tenant_schema,
            student_id,
            query,
        ).await
    }

    /// 获取学习建议
    pub async fn get_study_recommendations(
        &self,
        tenant_schema: &str,
        student_id: Uuid,
        query: &StudentPerformanceQuery,
    ) -> Result<Vec<StudyRecommendation>> {
        StudentPerformanceRepository::get_study_recommendations(
            &self.pool,
            tenant_schema,
            student_id,
            query,
        ).await
    }

    /// 获取学习进步轨迹
    pub async fn get_progress_tracks(
        &self,
        tenant_schema: &str,
        student_id: Uuid,
        query: &StudentPerformanceQuery,
    ) -> Result<Vec<ProgressTrack>> {
        StudentPerformanceRepository::get_progress_tracks(
            &self.pool,
            tenant_schema,
            student_id,
            query,
        ).await
    }

    /// 获取完整的学生表现分析数据
    pub async fn get_complete_performance_analysis(
        &self,
        tenant_schema: &str,
        student_id: Uuid,
        query: &StudentPerformanceQuery,
    ) -> Result<StudentPerformanceAnalysis> {
        let knowledge_points = self.get_knowledge_point_mastery(tenant_schema, student_id, query).await?;
        let subject_abilities = self.get_subject_abilities(tenant_schema, student_id, query).await?;
        let recommendations = self.get_study_recommendations(tenant_schema, student_id, query).await?;
        let progress_tracks = self.get_progress_tracks(tenant_schema, student_id, query).await?;

        Ok(StudentPerformanceAnalysis {
            knowledge_points,
            subject_abilities,
            recommendations,
            progress_tracks,
        })
    }
}

/// 完整的学生表现分析数据结构
#[derive(Debug, serde::Serialize)]
pub struct StudentPerformanceAnalysis {
    pub knowledge_points: Vec<KnowledgePointMastery>,
    pub subject_abilities: Vec<SubjectAbility>,
    pub recommendations: Vec<StudyRecommendation>,
    pub progress_tracks: Vec<ProgressTrack>,
}
