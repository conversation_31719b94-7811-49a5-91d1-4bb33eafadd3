import { Paper } from '@/types/papers'
import { useEffect, useState } from 'react'
import { PublicAnswerSheet_1_0_0 } from './v1.0.0/PublicAnswerSheet_1_0_0'

export interface Props {
  answerSheetId: string
}

/**
 * 作者：张瀚
 * 说明：教辅内的题卡编辑
 */
export function PublicAnswerSheet({ answerSheetId }: Props) {
  //试卷数据
  const [paper, setPaper] = useState<Paper | undefined>(undefined)
  //答题卡版本
  const [answerCardVersion, setAnswerCardVersion] = useState<string>(PublicAnswerSheet_1_0_0.Version)
  useEffect(() => {
    //查询题卡和关联的内容块
    console.log(111, answerSheetId)
  }, [answerSheetId])

  if (!paper) {
    //加载中
    return
  }
  switch (answerCardVersion) {
    case PublicAnswerSheet_1_0_0.Version:
      return <PublicAnswerSheet_1_0_0.Editor paper={paper}></PublicAnswerSheet_1_0_0.Editor>
  }
  return <>不支持的版本：{answerCardVersion}</>
}
