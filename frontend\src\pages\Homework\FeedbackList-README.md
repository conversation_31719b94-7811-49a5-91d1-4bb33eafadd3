# FeedbackList 功能实现说明

## 实现的功能

### 1. 状态标签过滤方法

- 根据下拉筛选框的状态值过滤反馈列表
- 支持的状态：全部状态、已提交、已处理、已拒绝、已关闭、重新打开
- 筛选后自动调用 `getPageList` 接口更新数据

### 2. 状态标签渲染

实现了 `getStatusBadgeConfig` 函数，为不同状态提供不同的颜色标签：

- **已提交 (Initial)**: 蓝色标签 (`bg-blue-100 text-blue-800`)
- **已处理 (Received)**: 绿色标签 (`bg-green-100 text-green-800`)
- **已拒绝 (Rejected)**: 红色标签 (`bg-red-100 text-red-800`)
- **已关闭 (Cancelled)**: 灰色标签 (`bg-gray-100 text-gray-800`)
- **重新打开 (Resubmitted)**: 橙色标签 (`bg-orange-100 text-orange-800`)

### 3. Hover 交互功能

- 当反馈状态为"已提交"时，鼠标悬停在标签上会显示"拒绝反馈"按钮
- 使用 `Popover` 组件实现悬停效果，通过 `onMouseEnter` 和 `onMouseLeave` 事件控制
- 只有"已提交"状态的反馈才显示拒绝选项
- 悬停框显示在标签上方，避免遮挡其他内容

### 4. 二次确认机制

- 点击"拒绝反馈"按钮时弹出确认对话框
- 使用 `AlertDialog` 组件实现
- 确认后调用 `studentScoreApi.updateFeedback` 接口

### 5. 状态筛选优化

- 使用对象映射的方式优化下拉框选项的写法
- 支持动态渲染状态选项
- 筛选变化时自动触发数据刷新

## 技术实现

### 核心函数

#### `getStatusBadgeConfig(status: HomeworkFeedbackStatus)`

返回状态标签的配置信息，包括颜色、样式和显示文本。

#### `handleRejectFeedback()`

处理拒绝反馈的异步操作：

1. 查找选中的反馈项
2. 调用 `updateFeedback` 接口，只更新 `status` 为 "Rejected"
3. 保持原有的 `text` 内容不变
4. 成功后刷新列表并显示成功提示

#### `handleRejectClick(feedbackId: string)`

处理点击拒绝按钮的操作，设置选中的反馈 ID 并打开确认对话框。

### 状态管理

- `rejectConfirmOpen`: 控制确认对话框的显示状态
- `selectedFeedbackId`: 存储当前选中要拒绝的反馈 ID
- `statusFilter`: 当前选中的状态筛选值
- `popoverOpen`: 控制哪个反馈项的悬停框处于打开状态（存储反馈 ID）

### 接口交互

- 筛选状态变化时，`useEffect` 自动触发 `getPageList` 调用
- `getPageList` 接口根据 `statusFilter` 传递相应的 `status_list` 参数
- 拒绝操作调用 `updateFeedback` 接口，只更新状态字段

## 使用方式

1. **查看不同状态的反馈**：使用顶部的状态筛选下拉框
2. **拒绝学生反馈**：
   - 将鼠标悬停在"已提交"状态的标签上
   - 点击出现的"拒绝反馈"按钮
   - 在确认对话框中点击"确认拒绝"

## 注意事项

- 只有状态为"已提交"的反馈才能被拒绝
- 拒绝操作会保持原有的反馈文本内容不变
- 状态筛选会自动触发数据刷新，无需手动点击查询按钮
- 所有操作都有相应的成功/失败提示
