CREATE TABLE IF NOT EXISTS "tenant_paper" (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    paper_name text NOT NULL,
    year int2 NOT NULL,
    "subject_code" varchar(20) NOT NULL,
    grade_level_code varchar(20),
    section_id uuid NOT NULL,
    public_id uuid,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
COMMENT ON COLUMN "tenant_paper".subject_code IS '学科';
COMMENT ON COLUMN "tenant_paper".grade_level_code IS '年级';
COMMENT ON COLUMN "tenant_paper".section_id IS '内容块ID';
COMMENT ON COLUMN "tenant_paper".public_id IS '公共卷ID';

CREATE TABLE IF NOT EXISTS "tenant_section" (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "items" jsonb NOT NULL,
    public_id uuid,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
CREATE TABLE IF NOT EXISTS "tenant_question" (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "question_type_code" varchar(20) NOT NULL,
    "items" jsonb NOT NULL,
    "subject_code" varchar(20) NOT NULL,
    public_id uuid,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS "tenant_question_answer" (
    "id" uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    "question_id" uuid NOT NULL,
    "answer_area_id" int2 NOT NULL,
    "content" text NOT NULL,
    "explanation" text,
    public_id uuid,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);