use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use sqlx::types::Json;
use uuid::Uuid;
use crate::model::public_resource::question::QuestionUnit;

#[derive(Debug, Serialize, Deserialize, Clone, FromRow)]
#[serde( rename_all = "camelCase")]
pub struct TenantQuestion {
    pub id: Uuid,
    pub question_type_code: String,
    pub subject_code: String,
    pub items: Json<Vec<QuestionUnit>>,
    pub updated_at: Option<DateTime<Utc>>,
    pub public_id: Option<Uuid>, // 公共域id
}