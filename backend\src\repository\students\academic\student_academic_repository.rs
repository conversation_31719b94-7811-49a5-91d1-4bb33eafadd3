use anyhow::Result;
use sqlx::{PgPool, Row};
use uuid::Uuid;

use crate::model::analytics::student_academic::{
    StudentAcademicOverview, StudentExamScore, StudentHomeworkRecord, 
    ScoreTrendData, StudentAcademicQuery
};

pub struct StudentAcademicRepository;

impl StudentAcademicRepository {
    /// 获取学生学术表现概览
    pub async fn get_academic_overview(
        pool: &PgPool,
        tenant_schema: &str,
        student_id: Uuid,
    ) -> Result<StudentAcademicOverview> {
        let query = format!(
            r#"
            WITH student_scores AS (
                SELECT 
                    hs.student_id,
                    h.id as exam_id,
                    COALESCE(
                        (hs.score),
                        0
                    ) as score,
                    hs.class_id
                FROM {}.homework_students hs
                JOIN {}.homework h ON hs.homework_id = h.id
                WHERE hs.student_id = $1
            ),
            score_stats AS (
                SELECT 
                    student_id,
                    COUNT(DISTINCT homework_id) as total_exams,
                    AVG(score)::float as average_score,
                    class_id
                FROM {}.homework_students
                GROUP BY student_id, class_id
            ),
            class_rankings AS (
                SELECT 
                    hs.student_id,
                    ROW_NUMBER() OVER (ORDER BY AVG(
                        COALESCE(hs.score, 0)
                    ) DESC) as class_rank
                FROM {}.homework_students hs
                JOIN {}.homework h ON hs.homework_id = h.id
                WHERE hs.class_id = (SELECT class_id FROM {}.student_teaching_classes WHERE student_id = $1)
                GROUP BY hs.student_id
            ),
            homework_stats AS (
                SELECT 
                    COUNT(*) as total_homework,
                    COUNT(CASE WHEN hs.status = 'completed' THEN 1 END) as completed_homework
                FROM {}.homework_students hs
                JOIN {}.homework h ON hs.homework_id = h.id
                WHERE hs.student_id = $1
                AND h.created_at >= NOW() - INTERVAL '30 days'
            )
            SELECT 
                $1 as student_id,
                COALESCE(ss.total_exams, 0) as total_exams,
                COALESCE(ss.average_score, 0.0)::float as average_score,
                COALESCE(cr.class_rank, 0)::int as class_rank,
                0 as grade_rank,
                'stable' as improvement_trend,
                0.0::float as improvement_rate,
                CASE 
                    WHEN hws.total_homework > 0 THEN 
                        (hws.completed_homework::float / hws.total_homework::float * 100)::float
                    ELSE 0.0::float 
                END as homework_completion_rate
            FROM homework_stats hws
            LEFT JOIN score_stats ss ON ss.student_id = $1
            LEFT JOIN class_rankings cr ON cr.student_id = $1
            "#,
            tenant_schema, tenant_schema, tenant_schema, tenant_schema, 
            tenant_schema, tenant_schema, tenant_schema,tenant_schema
        );

        let row = sqlx::query(&query)
            .bind(student_id)
            .fetch_one(pool)
            .await?;

        Ok(StudentAcademicOverview {
            student_id: row.get("student_id"),
            total_exams: row.get("total_exams"),
            average_score: row.get("average_score"),
            class_rank: row.get("class_rank"),
            grade_rank: row.get("grade_rank"),
            improvement_trend: row.get("improvement_trend"),
            improvement_rate: row.get("improvement_rate"),
            homework_completion_rate: row.get("homework_completion_rate"),
        })
    }

    /// 获取学生考试成绩列表
    pub async fn get_exam_scores(
        pool: &PgPool,
        tenant_schema: &str,
        student_id: Uuid,
        query_params: &StudentAcademicQuery,
    ) -> Result<Vec<StudentExamScore>> {
        let limit = query_params.limit.unwrap_or(10);
        
        let sql_query = format!(
            r#"
            WITH student_exam_scores AS (
                SELECT 
                    hs.id,
                    hs.student_id,
                    h.id as homework_id,
                    hsub.subject_id,
                    s.name as subject_name,
                    COALESCE(hs.score, 0) as score,
                    hsub.total_score as total_possible,
                    h.created_at as exam_date,
                    h.homework_name as exam_name,
                    ROW_NUMBER() OVER (
                        PARTITION BY hsub.subject_id
                        ORDER BY (
                             COALESCE(hs.score, 0)
                        ) DESC
                    ) as subject_rank
                FROM {}.homework_students hs
                JOIN {}.homework h ON hs.homework_id = h.id
                JOIN {}.homework_subjects hsub ON hsub.homework_id = h.id
                JOIN public.subjects s ON hsub.subject_id = s.id
                WHERE hs.student_id = $1
                ORDER BY h.created_at DESC
                LIMIT $2
            )
            SELECT 
                id,
                student_id,
                homework_id,
                subject_id,
                subject_name,
                score::float as score,
                total_possible,
                (score / total_possible * 100)::float as percentage,
                subject_rank::int as rank,
                exam_date,
                exam_name
            FROM  student_exam_scores
            ORDER BY exam_date DESC
            "#,
            tenant_schema, tenant_schema, tenant_schema
        );

        let rows = sqlx::query(&sql_query)
            .bind(student_id)
            .bind(limit)
            .fetch_all(pool)
            .await?;

        let mut exam_scores = Vec::new();
        for row in rows {
            exam_scores.push(StudentExamScore {
                id: row.get("id"),
                student_id: row.get("student_id"),
                exam_id: row.get("exam_id"),
                subject_id: row.get("subject_id"),
                subject_name: row.get("subject_name"),
                score: row.get("score"),
                total_possible: row.get("total_possible"),
                percentage: row.get("percentage"),
                rank: row.get("rank"),
                exam_date: row.get("exam_date"),
                exam_name: row.get("exam_name"),
            });
        }

        Ok(exam_scores)
    }

    /// 获取学生作业记录
    pub async fn get_homework_records(
        pool: &PgPool,
        tenant_schema: &str,
        student_id: Uuid,
        query_params: &StudentAcademicQuery,
    ) -> Result<Vec<StudentHomeworkRecord>> {
        let limit = query_params.limit.unwrap_or(10);
        
        let sql_query = format!(
            r#"
            SELECT 
                hs.id,
                h.id as homework_id,
                hs.student_id,
                h.homework_name,
                COALESCE(s.name, '未知科目') as subject_name,
                h.created_at + INTERVAL '7 days' as due_date,
                CASE 
                    WHEN hs.status = 'completed' THEN 'completed'
                    WHEN h.created_at + INTERVAL '7 days' < NOW() THEN 'overdue'
                    ELSE 'pending'
                END as status,
                COALESCE(hs.score, 0) as score,
                100.0 as total_score,
                hs.updated_at as submitted_at
            FROM {}.homework_students hs
            JOIN {}.homework h ON hs.homework_id = h.id
            LEFT JOIN {}.subject_groups sg ON h.subject_group_id = sg.id
            JOIN public.subjects s ON sg.subject_code = s.code
            WHERE hs.student_id = $1
            ORDER BY h.created_at DESC
            LIMIT $2
            "#,
            tenant_schema, tenant_schema, tenant_schema
        );

        let rows = sqlx::query(&sql_query)
            .bind(student_id)
            .bind(limit)
            .fetch_all(pool)
            .await?;

        let mut homework_records = Vec::new();
        for row in rows {
            homework_records.push(StudentHomeworkRecord {
                id: row.get("id"),
                homework_id: row.get("homework_id"),
                student_id: row.get("student_id"),
                homework_name: row.get("homework_name"),
                subject_name: row.get("subject_name"),
                due_date: row.get("due_date"),
                status: row.get("status"),
                score: row.get::<Option<f64>, _>("score").map(|s| s as f32),
                total_score: row.get::<Option<f32>, _>("total_score"),
                submitted_at: row.get("submitted_at"),
            });
        }

        Ok(homework_records)
    }

    /// 获取学科成绩趋势数据
    pub async fn get_subject_trend_data(
        pool: &PgPool,
        tenant_schema: &str,
        student_id: Uuid,
        date_range: &str,
    ) -> Result<Vec<ScoreTrendData>> {
        let days = match date_range {
            "30d" => 30,
            "90d" => 90,
            "180d" => 180,
            "1y" => 365,
            _ => 90,
        };

        let sql_query = format!(
            r#"
            SELECT 
                hsub.subject_id,
                s.name as subject_name,
                h.created_at as exam_date,
                COALESCE(hs.score, 0) as score,
                hsub.total_score as total_possible
            FROM {}.homework_students hs
            JOIN {}.homework h ON hs.homework_id = h.id
            JOIN {}.homework_subjects hsub ON hsub.homework_id = h.id
            JOIN public.subjects s ON hsub.subject_id = s.id
            WHERE hs.student_id = $1
            AND h.created_at >= NOW() - INTERVAL '{} days'
            ORDER BY s.code, h.created_at
            "#,
            tenant_schema, tenant_schema, tenant_schema, days
        );

        let rows = sqlx::query(&sql_query)
            .bind(student_id)
            .fetch_all(pool)
            .await?;

        let mut trend_data = Vec::new();
        for row in rows {
            trend_data.push(ScoreTrendData {
                subject_id: row.get("subject_id"),
                subject_name: row.get("subject_name"),
                exam_date: row.get("exam_date"),
                score: row.get::<f64, _>("score") as f32,
                total_possible: row.get("total_possible"),
            });
        }

        Ok(trend_data)
    }
}
