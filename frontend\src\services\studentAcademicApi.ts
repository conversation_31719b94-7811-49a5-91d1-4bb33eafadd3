import apiClient from './apiClient';

// 学生学术数据相关类型定义
export interface StudentAcademicOverview {
  student_id: string;
  total_exams: number;
  average_score: number;
  class_rank: number;
  grade_rank: number;
  improvement_trend: string; // 修改为 string 类型以匹配后端
  improvement_rate: number;
  homework_completion_rate: number;
}

export interface StudentExamScore {
  id: string;
  student_id: string;
  exam_id: string;
  subject_id: string;
  subject_name: string;
  score: number;
  total_possible: number;
  percentage: number;
  rank: number;
  exam_date: string;
  exam_name: string;
}

export interface SubjectPerformanceDataPoint {
  date: string;
  score: number;
  percentage: number;
}

export interface StudentSubjectPerformance {
  subject: string;
  subject_id: string;
  latest_score: number;
  trend: string; // 修改为 string 类型以匹配后端
  data: SubjectPerformanceDataPoint[];
}

export interface StudentHomeworkRecord {
  id: string;
  homework_id: string;
  student_id: string;
  homework_name: string;
  subject_name: string;
  due_date: string;
  status: 'completed' | 'pending' | 'overdue';
  score?: number;
  total_score?: number;
  submitted_at?: string;
}

export interface StudentScoreStatistics {
  overview: StudentAcademicOverview;
  recent_scores: StudentExamScore[];
}

export interface StudentPerformanceAnalysis {
  subject_performance: StudentSubjectPerformance[];
  improvement_suggestions: string[];
}

export interface StudentHomeworkStatus {
  homework_records: StudentHomeworkRecord[];
  completion_rate: number;
  pending_count: number;
  overdue_count: number;
}

export interface StudentAcademicQuery {
  limit?: number;
  subject_id?: string;
  date_range?: '30d' | '90d' | '180d' | '1y';
  exam_type?: string;
}

export interface StudentAcademicOverviewResponse {
  score_statistics: StudentScoreStatistics;
  performance_analysis: StudentPerformanceAnalysis;
  homework_status: StudentHomeworkStatus;
}

// API服务类
export class StudentAcademicApi {
  /**
   * 获取学生成绩统计信息
   */
  static async getScoreStatistics(
    studentId: string,
    params?: StudentAcademicQuery
  ): Promise<StudentScoreStatistics> {
    const response = await apiClient.get(`/students/${studentId}/scores`, {
      params
    });
    return response.data.data;
  }

  /**
   * 获取学生表现分析
   */
  static async getPerformanceAnalysis(
    studentId: string,
    params?: StudentAcademicQuery
  ): Promise<StudentPerformanceAnalysis> {
    const response = await apiClient.get(`/students/${studentId}/performance`, {
      params
    });
    return response.data.data;
  }

  /**
   * 获取学生作业状态
   */
  static async getHomeworkStatus(
    studentId: string,
    params?: StudentAcademicQuery
  ): Promise<StudentHomeworkStatus> {
    const response = await apiClient.get(`/students/${studentId}/homework`, {
      params
    });
    return response.data.data;
  }

  /**
   * 获取学生学术数据综合概览（用于仪表板）
   */
  static async getAcademicOverview(
    studentId: string,
    params?: StudentAcademicQuery
  ): Promise<StudentAcademicOverviewResponse> {
    const response = await apiClient.get(`/students/${studentId}/academic-overview`, {
      params
    });
    return response.data.data;
  }

  /**
   * 获取学生最近考试成绩（简化版本）
   */
  static async getRecentScores(
    studentId: string,
    limit: number = 10
  ): Promise<StudentExamScore[]> {
    const statistics = await this.getScoreStatistics(studentId, { limit });
    return statistics.recent_scores;
  }

  /**
   * 获取学生学科趋势数据（简化版本）
   */
  static async getSubjectTrends(
    studentId: string,
    dateRange: '30d' | '90d' | '180d' | '1y' = '90d'
  ): Promise<StudentSubjectPerformance[]> {
    const analysis = await this.getPerformanceAnalysis(studentId, { date_range: dateRange });
    return analysis.subject_performance;
  }

  /**
   * 获取学生作业记录（简化版本）
   */
  static async getHomeworkRecords(
    studentId: string,
    limit: number = 10
  ): Promise<StudentHomeworkRecord[]> {
    const status = await this.getHomeworkStatus(studentId, { limit });
    return status.homework_records;
  }

  // ==================== 当前登录学生自查接口 ====================

  /**
   * 获取当前登录学生的成绩统计信息
   */
  static async getMyScoreStatistics(
    params?: StudentAcademicQuery
  ): Promise<StudentExamScore[]> {
    const response = await apiClient.get('/api/v1/profile/scores', {
      params
    });
    return response.data;
  }

  /**
   * 获取当前登录学生的表现分析
   */
  static async getMyPerformanceAnalysis(
    params?: StudentAcademicQuery
  ): Promise<any[]> {
    const response = await apiClient.get('/api/v1/profile/performance', {
      params
    });
    return response.data;
  }

  /**
   * 获取当前登录学生的作业状态
   */
  static async getMyHomeworkStatus(
    params?: StudentAcademicQuery
  ): Promise<StudentHomeworkRecord[]> {
    const response = await apiClient.get('/api/v1/profile/homework', {
      params
    });
    return response.data;
  }

  /**
   * 获取当前登录学生的学术数据综合概览
   */
  static async getMyAcademicOverview(): Promise<StudentAcademicOverview> {
    try {
      const response = await apiClient.get('/api/v1/profile/academic-overview');
      return response.data;
    } catch (error) {
      console.error('💥 Academic Overview API 请求失败:', error);
      throw error;
    }
  }

  /**
   * 获取当前登录学生的最近考试成绩（简化版本）
   */
  static async getMyRecentScores(
    limit: number = 10
  ): Promise<StudentExamScore[]> {
    return this.getMyScoreStatistics({ limit });
  }

  /**
   * 获取当前登录学生的学科趋势数据（简化版本）
   */
  static async getMySubjectTrends(
    dateRange: '30d' | '90d' | '180d' | '1y' = '90d'
  ): Promise<any[]> {
    return this.getMyPerformanceAnalysis({ date_range: dateRange });
  }

  /**
   * 获取当前登录学生的作业记录（简化版本）
   */
  static async getMyHomeworkRecords(
    limit: number = 10
  ): Promise<StudentHomeworkRecord[]> {
    return this.getMyHomeworkStatus({ limit });
  }
}

export default StudentAcademicApi;
