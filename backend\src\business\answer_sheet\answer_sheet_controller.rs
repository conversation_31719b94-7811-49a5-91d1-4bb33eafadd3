use axum::{extract::State, routing::post, <PERSON>son, Router};

use crate::{
    business::answer_sheet::{
        answer_sheet_repository::AnswerSheetModel,
        answer_sheet_service::{AnswerSheetService, InsertParams},
    },
    utils::api_response::{responses, ApiResponse},
    web_server::AppState,
};

pub fn create_router() -> Router<AppState> {
    Router::new().route("/createAnswerSheet", post(create_answer_sheet))
}

/**
 * 作者：张瀚
 * 说明：创建答题卡
 */
pub async fn create_answer_sheet(State(state): State<AppState>, Json(params): Json<InsertParams>) -> Result<ApiResponse<AnswerSheetModel>, ApiResponse<String>> {
    AnswerSheetService::create_answer_sheet(&state.db, params)
        .await
        .map_err(|e| responses::error(&e, None))
        .map(|data| responses::success(data, None))
}
