import { Annotation } from '@/components/question-card/v1.0.0/entity/annotation-vo'
import { AnswerCardVO, RenderData } from '@/components/question-card/v1.0.0/entity/answer-card-vo'
import { createApiHeaders } from '@/lib/apiUtils'
import { ApiResponse } from '@/types'
import apiClient from './apiClient'

const API_BASE_URL = '/api/v1'

/**
 * 作者：张瀚
 * 说明：公共域的答题卡接口
 */
export const answerSheetApi = {
  /**
   * 作者：张瀚
   * 说明：创建答题卡
   */
  createAnswerSheet: async (tenant_id: string, params: InsertParams): Promise<ApiResponse<AnswerCardVO>> => {
    return apiClient.post(`${API_BASE_URL}/tenants/public/answerSheet/createAnswerSheet`, params, {
      headers: createApiHeaders(tenant_id),
    })
  },
}

export interface InsertParams {
  sectionId: String
  dataVersion: String
  ///渲染数据,根据版本,1.0.0版本使用的是RenderData
  renderData: RenderData
  ///渲染数据,根据版本,1.0.0版本使用的是Annotation
  annotation?: Annotation
}
