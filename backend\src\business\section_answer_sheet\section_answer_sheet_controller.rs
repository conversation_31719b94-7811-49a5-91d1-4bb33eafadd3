use axum::{extract::State, routing::post, Json, Router};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use crate::{
    business::section_answer_sheet::section_answer_sheet_repository::SectionAnswerSheetRepository,
    utils::api_response::{responses, ApiResponse},
    web_server::AppState,
};

pub fn create_router() -> Router<AppState> {
    Router::new().route("/findBySectionId", post(find_by_section_id))
}

/**
 * 作者：张瀚
 * 说明：查询内容块关联的答题卡ID列表
 */
pub async fn find_by_section_id(State(state): State<AppState>, Json(params): Json<FindBySectionIdParams>) -> Result<ApiResponse<Vec<Uuid>>, ApiResponse<String>> {
    SectionAnswerSheetRepository::find_by_section_id(&state.db, &params.section_id)
        .await
        .map_err(|e| responses::error(&e, None))
        .map(|data| responses::success(data, None))
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub struct FindBySectionIdParams {
    pub section_id: Uuid,
}
