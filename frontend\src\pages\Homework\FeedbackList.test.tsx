import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi } from 'vitest'
import FeedbackList from './FeedbackList'
import { studentScoreApi } from '@/services/studentScoreApi'
import { toast } from 'sonner'

// Mock dependencies
vi.mock('@/services/studentScoreApi')
vi.mock('sonner')
vi.mock('@/lib/apiUtils', () => ({
  getTenantInfoFromLocalStorage: () => ({ schema_name: 'test_schema' }),
}))
vi.mock('@/stores', () => ({
  useHomeworkStore: () => ({ homework: null }),
}))
vi.mock('react-router-dom', () => ({
  useNavigate: () => vi.fn(),
}))

const mockFeedbackList = [
  {
    id: '1',
    homework_id: 'hw1',
    homework_name: '数学作业1',
    student_id: 'student1',
    score_id: 'score1',
    text: '这道题我不理解',
    status: 'Initial' as const,
    score: {
      id: 'score1',
      score: 85,
      student: {
        student_id: 'student1',
        student_name: '张三',
        student_number: '20230001',
      },
      blocks: [],
    },
  },
  {
    id: '2',
    homework_id: 'hw1',
    homework_name: '数学作业1',
    student_id: 'student2',
    score_id: 'score2',
    text: '答案有问题',
    status: 'Rejected' as const,
    score: {
      id: 'score2',
      score: 75,
      student: {
        student_id: 'student2',
        student_name: '李四',
        student_number: '20230002',
      },
      blocks: [],
    },
  },
]

describe('FeedbackList', () => {
  beforeEach(() => {
    vi.clearAllMocks()

    // Mock API responses
    vi.mocked(studentScoreApi.getTeachingClasses).mockResolvedValue({
      success: true,
      data: [{ id: 'class1', class_name: '一年级1班' }],
    })

    vi.mocked(studentScoreApi.getPageList).mockResolvedValue({
      success: true,
      data: mockFeedbackList,
      pagination: { total: 2 },
    })

    vi.mocked(studentScoreApi.updateFeedback).mockResolvedValue({
      success: true,
      data: { ...mockFeedbackList[0], status: 'Rejected' },
    })
  })

  test('renders feedback list with status badges', async () => {
    render(<FeedbackList />)

    await waitFor(() => {
      expect(screen.getByText('已提交')).toBeInTheDocument()
      expect(screen.getByText('已拒绝')).toBeInTheDocument()
    })
  })

  test('shows reject option on hover for Initial status', async () => {
    render(<FeedbackList />)

    await waitFor(() => {
      const initialBadge = screen.getByText('已提交')
      expect(initialBadge).toBeInTheDocument()

      // Hover should show reject button
      fireEvent.mouseEnter(initialBadge)
    })

    await waitFor(() => {
      expect(screen.getByText('拒绝反馈')).toBeInTheDocument()
    })
  })

  test('handles feedback rejection with confirmation', async () => {
    render(<FeedbackList />)

    await waitFor(() => {
      const initialBadge = screen.getByText('已提交')
      fireEvent.mouseEnter(initialBadge)
    })

    // Click reject button
    const rejectButton = screen.getByText('拒绝反馈')
    fireEvent.click(rejectButton)

    // Confirmation dialog should appear
    expect(screen.getByText('确认拒绝反馈')).toBeInTheDocument()

    // Confirm rejection
    const confirmButton = screen.getByText('确认拒绝')
    fireEvent.click(confirmButton)

    await waitFor(() => {
      expect(studentScoreApi.updateFeedback).toHaveBeenCalledWith('test_schema', {
        id: '1',
        status: 'Rejected',
        text: '这道题我不理解',
      })
      expect(toast.success).toHaveBeenCalledWith('反馈状态已更新为已拒绝')
    })
  })

  test('filters feedback by status', async () => {
    render(<FeedbackList />)

    // Wait for initial load
    await waitFor(() => {
      expect(screen.getByText('已提交')).toBeInTheDocument()
    })

    // Change status filter
    const statusSelect = screen.getByRole('combobox')
    fireEvent.click(statusSelect)

    const rejectedOption = screen.getByText('已拒绝')
    fireEvent.click(rejectedOption)

    await waitFor(() => {
      expect(studentScoreApi.getPageList).toHaveBeenCalledWith('test_schema', {
        homework_ids: [],
        teaching_class_ids: ['class1'],
        page_params: { page: 1, page_size: 20 },
        status_list: ['Rejected'],
      })
    })
  })
})
