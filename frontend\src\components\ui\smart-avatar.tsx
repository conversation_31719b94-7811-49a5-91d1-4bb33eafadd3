import * as React from "react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { generateAvatarProps } from "@/utils/avatarUtils"
import { cn } from "@/lib/utils"

interface SmartAvatarProps extends React.ComponentProps<typeof Avatar> {
    /**
     * 用户名，用于生成头像颜色和首字母
     */
    username: string;
    /**
     * 头像URL，网络地址，如果无效则自动生成头像
     */
    avatarUrl?: string | null;
    /**
     * 头像大小，会影响字体大小
     */
    size?: 'sm' | 'md' | 'lg' | 'xl';
    /**
     * 是否显示在线状态指示器
     */
    showStatus?: boolean;
    /**
     * 在线状态
     */
    isOnline?: boolean;
}

const sizeClasses = {
    sm: "h-6 w-6 text-xs",
    md: "h-8 w-8 text-sm",
    lg: "h-10 w-10 text-base",
    xl: "h-12 w-12 text-lg"
};

const statusSizeClasses = {
    sm: "h-2 w-2",
    md: "h-2.5 w-2.5",
    lg: "h-3 w-3",
    xl: "h-3.5 w-3.5"
};

export const SmartAvatar = React.forwardRef<
    React.ElementRef<typeof Avatar>,
    SmartAvatarProps
>(({
    username,
    avatarUrl,
    size = 'md',
    showStatus = false,
    isOnline = true,
    className,
    ...props
}, ref) => {
    const avatarProps = generateAvatarProps(username, avatarUrl);

    return (
        <div className="relative inline-block">
            <Avatar
                ref={ref}
                className={cn(sizeClasses[size], "rounded-lg", className)}
                {...props}
            >
                {avatarProps.src && (
                    <AvatarImage
                        src={avatarProps.src}
                        alt={username}
                        onError={(e) => {
                            // 如果图片加载失败，隐藏图片元素让fallback显示
                            const target = e.target as HTMLImageElement;
                            target.style.display = 'none';
                        }}
                    />
                )}
                <AvatarFallback
                    className="rounded-lg font-semibold"
                    style={avatarProps.fallbackStyle}
                >
                    {avatarProps.fallbackText}
                </AvatarFallback>
            </Avatar>

            {showStatus && (
                <div
                    className={cn(
                        "absolute -bottom-0.5 -right-0.5 rounded-full border-2 border-white",
                        statusSizeClasses[size],
                        isOnline ? "bg-green-400" : "bg-gray-400"
                    )}
                    aria-label={isOnline ? "在线" : "离线"}
                />
            )}
        </div>
    );
});

SmartAvatar.displayName = "SmartAvatar"; 