use axum::{
    extract::{Query, State},
    http::StatusCode,
    response::Json,
    routing::get,
    Router,
};
use serde_json::{json, Value};
use uuid::Uuid;

use crate::middleware::auth_middleware::AuthExtractor;
use crate::middleware::tenant_middleware::TenantExtractor;
use crate::model::analytics::student_performance::StudentPerformanceQuery;
use crate::service::student::performance::student_performance_service::StudentPerformanceService;
use crate::utils::api_response::ApiResponse;
use crate::web_server::AppState;

/// 获取学生知识点掌握情况
pub async fn get_knowledge_point_mastery(
    State(state): State<AppState>,
    AuthExtractor(user): AuthExtractor,
    TenantExtractor(tenant): TenantExtractor,
    Query(query): Query<StudentPerformanceQuery>,
) -> Result<Json<ApiResponse<Value>>, StatusCode> {
    let service = StudentPerformanceService::new(state.db.clone());
    
    match service.get_knowledge_point_mastery(
        &tenant.schema_name,
        user.user_id,
        &query,
    ).await {
        Ok(knowledge_points) => Ok(Json(ApiResponse::success(
            json!(knowledge_points),
            Some("获取知识点掌握情况成功".to_string()),
        ))),
        Err(e) => {
            tracing::error!("获取知识点掌握情况失败: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

/// 获取学科能力雷达图数据
pub async fn get_subject_abilities(
    State(state): State<AppState>,
    AuthExtractor(user): AuthExtractor,
    TenantExtractor(tenant): TenantExtractor,
    Query(query): Query<StudentPerformanceQuery>,
) -> Result<Json<ApiResponse<Value>>, StatusCode> {
    let service = StudentPerformanceService::new(state.db.clone());
    
    match service.get_subject_abilities(
        &tenant.schema_name,
        user.user_id,
        &query,
    ).await {
        Ok(subject_abilities) => Ok(Json(ApiResponse::success(
            json!(subject_abilities),
             Some("获取学科能力数据成功".to_string()),
        ))),
        Err(e) => {
            tracing::error!("获取学科能力数据失败: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

/// 获取学习建议
pub async fn get_study_recommendations(
    State(state): State<AppState>,
    AuthExtractor(user): AuthExtractor,
    TenantExtractor(tenant): TenantExtractor,
    Query(query): Query<StudentPerformanceQuery>,
) -> Result<Json<ApiResponse<Value>>, StatusCode> {
    let service = StudentPerformanceService::new(state.db.clone());
    
    match service.get_study_recommendations(
        &tenant.schema_name,
        user.user_id,
        &query,
    ).await {
        Ok(recommendations) => Ok(Json(ApiResponse::success(
            json!(recommendations),
            Some("获取学习建议成功".to_string()),
        ))),
        Err(e) => {
            tracing::error!("获取学习建议失败: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

/// 获取学习进步轨迹
pub async fn get_progress_tracks(
    State(state): State<AppState>,
    AuthExtractor(user): AuthExtractor,
    TenantExtractor(tenant): TenantExtractor,
    Query(query): Query<StudentPerformanceQuery>,
) -> Result<Json<ApiResponse<Value>>, StatusCode> {
    let service = StudentPerformanceService::new(state.db.clone());
    
    match service.get_progress_tracks(
        &tenant.schema_name,
        user.user_id,
        &query,
    ).await {
        Ok(progress_tracks) => Ok(Json(ApiResponse::success(
            json!(progress_tracks),
           Some("获取学习进步轨迹成功".to_string())

        ))),
        Err(e) => {
            tracing::error!("获取学习进步轨迹失败: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

/// 获取完整的学生表现分析数据
pub async fn get_complete_performance_analysis(
    State(state): State<AppState>,
    AuthExtractor(user): AuthExtractor,
    TenantExtractor(tenant): TenantExtractor,
    Query(query): Query<StudentPerformanceQuery>,
) -> Result<Json<ApiResponse<Value>>, StatusCode> {
    let service = StudentPerformanceService::new(state.db.clone());
    
    match service.get_complete_performance_analysis(
        &tenant.schema_name,
        user.user_id,
        &query,
    ).await {
        Ok(analysis) => Ok(Json(ApiResponse::success(
            json!(analysis),
            Some("获取学生表现分析成功".to_string()),
        ))),
        Err(e) => {
            tracing::error!("获取学生表现分析失败: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

/// 获取指定学生的知识点掌握情况（管理员/教师使用）
pub async fn get_student_knowledge_point_mastery(
    State(state): State<AppState>,
    AuthExtractor(_user): AuthExtractor,
    TenantExtractor(tenant): TenantExtractor,
    axum::extract::Path(student_id): axum::extract::Path<Uuid>,
    Query(query): Query<StudentPerformanceQuery>,
) -> Result<Json<ApiResponse<Value>>, StatusCode> {
    let service = StudentPerformanceService::new(state.db.clone());
    
    match service.get_knowledge_point_mastery(
        &tenant.schema_name,
        student_id,
        &query,
    ).await {
        Ok(knowledge_points) => Ok(Json(ApiResponse::success(
            json!(knowledge_points),
            Some("获取学生知识点掌握情况成功".to_string()),
        ))),
        Err(e) => {
            tracing::error!("获取学生知识点掌握情况失败: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

/// 获取指定学生的学科能力数据（管理员/教师使用）
pub async fn get_student_subject_abilities(
    State(state): State<AppState>,
    AuthExtractor(_user): AuthExtractor,
    TenantExtractor(tenant): TenantExtractor,
    axum::extract::Path(student_id): axum::extract::Path<Uuid>,
    Query(query): Query<StudentPerformanceQuery>,
) -> Result<Json<ApiResponse<Value>>, StatusCode> {
    let service = StudentPerformanceService::new(state.db.clone());
    
    match service.get_subject_abilities(
        &tenant.schema_name,
        student_id,
        &query,
    ).await {
        Ok(subject_abilities) => Ok(Json(ApiResponse::success(
            json!(subject_abilities),
            Some("获取学生学科能力数据成功".to_string()),
        ))),
        Err(e) => {
            tracing::error!("获取学生学科能力数据失败: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

/// 获取指定学生的学习建议（管理员/教师使用）
pub async fn get_student_study_recommendations(
    State(state): State<AppState>,
    AuthExtractor(_user): AuthExtractor,
    TenantExtractor(tenant): TenantExtractor,
    axum::extract::Path(student_id): axum::extract::Path<Uuid>,
    Query(query): Query<StudentPerformanceQuery>,
) -> Result<Json<ApiResponse<Value>>, StatusCode> {
    let service = StudentPerformanceService::new(state.db.clone());
    
    match service.get_study_recommendations(
        &tenant.schema_name,
        student_id,
        &query,
    ).await {
        Ok(recommendations) => Ok(Json(ApiResponse::success(
            json!(recommendations),
            Some("获取学生学习建议成功".to_string()),
        ))),
        Err(e) => {
            tracing::error!("获取学生学习建议失败: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

/// 获取指定学生的进步轨迹（管理员/教师使用）
pub async fn get_student_progress_tracks(
    State(state): State<AppState>,
    AuthExtractor(_user): AuthExtractor,
    TenantExtractor(tenant): TenantExtractor,
    axum::extract::Path(student_id): axum::extract::Path<Uuid>,
    Query(query): Query<StudentPerformanceQuery>,
) -> Result<Json<ApiResponse<Value>>, StatusCode> {
    let service = StudentPerformanceService::new(state.db.clone());
    
    match service.get_progress_tracks(
        &tenant.schema_name,
        student_id,
        &query,
    ).await {
        Ok(progress_tracks) => Ok(Json(ApiResponse::success(
            json!(progress_tracks),
            Some("获取学生进步轨迹成功".to_string()),
        ))),
        Err(e) => {
            tracing::error!("获取学生进步轨迹失败: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

/// 获取指定学生的完整表现分析（管理员/教师使用）
pub async fn get_student_complete_performance_analysis(
    State(state): State<AppState>,
    AuthExtractor(_user): AuthExtractor,
    TenantExtractor(tenant): TenantExtractor,
    axum::extract::Path(student_id): axum::extract::Path<Uuid>,
    Query(query): Query<StudentPerformanceQuery>,
) -> Result<Json<ApiResponse<Value>>, StatusCode> {
    let service = StudentPerformanceService::new(state.db.clone());
    
    match service.get_complete_performance_analysis(
        &tenant.schema_name,
        student_id,
        &query,
    ).await {
        Ok(analysis) => Ok(Json(ApiResponse::success(
            json!(analysis),
            Some("获取学生完整表现分析成功".to_string()),
        ))),
        Err(e) => {
            tracing::error!("获取学生完整表现分析失败: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

/// 创建学生表现分析路由
pub fn create_router() -> Router<AppState> {
    Router::new()
        // 学生自己访问的路由
        .route("/knowledge-points", get(get_knowledge_point_mastery))
        .route("/subject-abilities", get(get_subject_abilities))
        .route("/recommendations", get(get_study_recommendations))
        .route("/progress-tracks", get(get_progress_tracks))
        .route("/complete-analysis", get(get_complete_performance_analysis))
        // 管理员/教师访问指定学生的路由
        .route("/students/{student_id}/knowledge-points", get(get_student_knowledge_point_mastery))
        .route("/students/{student_id}/subject-abilities", get(get_student_subject_abilities))
        .route("/students/{student_id}/recommendations", get(get_student_study_recommendations))
        .route("/students/{student_id}/progress-tracks", get(get_student_progress_tracks))
        .route("/students/{student_id}/complete-analysis", get(get_student_complete_performance_analysis))
}
