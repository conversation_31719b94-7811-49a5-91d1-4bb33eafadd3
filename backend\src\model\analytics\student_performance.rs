use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use bigdecimal::BigDecimal;

/// 知识点掌握情况
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct KnowledgePointMastery {
    pub point_name: String,
    pub subject: String,
    pub mastery_level: i32, // 0-100
    pub recent_practice_count: i32,
    pub improvement_trend: String, // 'up' | 'down' | 'stable'
    pub difficulty_level: String, // 'easy' | 'medium' | 'hard'
    pub recommended_practice_time: i32, // 分钟
}

/// 学科能力雷达图数据
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct SubjectAbility {
    pub subject: String,
    pub understanding: i32, // 理解能力
    pub application: i32,   // 应用能力
    pub analysis: i32,      // 分析能力
    pub synthesis: i32,     // 综合能力
    pub memory: i32,        // 记忆能力
}

/// 学习建议资源
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StudyResource {
    pub resource_type: String, // 'video' | 'exercise' | 'reading'
    pub title: String,
    pub url: String,
    pub duration: i32,
}

/// 学习建议
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StudyRecommendation {
    pub id: String,
    pub recommendation_type: String, // 'urgent' | 'important' | 'suggested'
    pub subject: String,
    pub title: String,
    pub description: String,
    pub estimated_time: i32, // 分钟
    pub difficulty: String, // 'easy' | 'medium' | 'hard'
    pub priority: i32, // 1-5
    pub resources: Vec<StudyResource>,
}

/// 进步轨迹
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct ProgressTrack {
    pub date: String,
    pub overall_score: BigDecimal,
    pub subject_scores: serde_json::Value, // JSON object with subject scores
    pub milestone_achieved: Option<String>,
    pub notes: Option<String>,
}

/// 学生表现分析查询参数
#[derive(Debug, Clone, Deserialize)]
pub struct StudentPerformanceQuery {
    pub time_range: Option<String>, // '30d' | '90d' | '180d' | '1y'
    pub subject: Option<String>,
}

/// 作业统计数据（用于计算知识点掌握）
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct HomeworkKnowledgePoint {
    pub subject_name: String,
    pub knowledge_point: String,
    pub total_questions: i32,
    pub correct_answers: i32,
    pub recent_homework_count: i32,
    pub avg_score: BigDecimal,
    pub difficulty_level: String,
    pub last_practice_date: DateTime<Utc>,
}

/// 学科表现统计
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct SubjectPerformanceStats {
    pub subject_name: String,
    pub total_homeworks: i32,
    pub avg_score: BigDecimal,
    pub understanding_score: i32,
    pub application_score: i32,
    pub analysis_score: i32,
    pub synthesis_score: i32,
    pub memory_score: i32,
}

/// 学生作业进度轨迹
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct StudentHomeworkProgress {
    pub homework_date: DateTime<Utc>,
    pub subject_name: String,
    pub homework_score: BigDecimal,
    pub class_avg_score: BigDecimal,
    pub homework_name: String,
}
