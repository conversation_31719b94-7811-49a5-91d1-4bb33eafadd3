import { useContext } from 'react';
import { AuthContext } from '@/contexts/AuthContext';

export interface UserRoleInfo {
    isStudent: boolean;
    isTeacher: boolean;
    isAdmin: boolean;
    isPrincipal: boolean;
    isParent: boolean;
    role: string | null;
    permissions: string[];
}

export const useUserRole = (): UserRoleInfo => {
    const authContext = useContext(AuthContext);

    if (!authContext || !authContext.userRole || !authContext.currentUserData) {
        return {
            isStudent: false,
            isTeacher: false,
            isAdmin: false,
            isPrincipal: false,
            isParent: false,
            role: null,
            permissions: []
        };
    }

    const { userRole } = authContext;
    const role = userRole || '';
    const permissions: string[] = []; // 暂时为空数组，后续可从其他地方获取

    return {
        isStudent: role === 'student',
        isTeacher: role === 'teacher',
        isAdmin: role === 'admin',
        isPrincipal: role === 'admin', // 校长也算管理员角色
        isParent: role === 'parent',
        role,
        permissions
    };
};

export default useUserRole; 