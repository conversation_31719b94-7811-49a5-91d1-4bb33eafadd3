use anyhow::Result;
use sqlx::PgPool;
use uuid::Uuid;

use crate::model::analytics::student_academic::{
    StudentAcademicOverview, StudentExamScore, StudentHomeworkRecord,
    StudentAcademicQuery
};
use crate::repository::students::academic::student_academic_repository::StudentAcademicRepository;

pub struct StudentAcademicService {
    pool: PgPool,
}

impl StudentAcademicService {
    /// 创建新的StudentAcademicService实例
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    /// 获取学生学术表现概览
    pub async fn get_academic_overview(
        &self,
        tenant_schema: &str,
        student_id: Uuid,
    ) -> Result<StudentAcademicOverview> {
        StudentAcademicRepository::get_academic_overview(
            &self.pool, 
            tenant_schema, 
            student_id
        ).await
    }

    /// 获取学生考试成绩列表
    pub async fn get_exam_scores(
        &self,
        tenant_schema: &str,
        student_id: Uuid,
        query_params: &StudentAcademicQuery,
    ) -> Result<Vec<StudentExamScore>> {
        StudentAcademicRepository::get_exam_scores(
            &self.pool, 
            tenant_schema, 
            student_id,
            query_params
        ).await
    }

    /// 获取学生学科表现分析
    pub async fn get_subject_performance(
        &self,
        tenant_schema: &str,
        student_id: Uuid,
        date_range: &str,
    ) -> Result<Vec<crate::model::analytics::student_academic::ScoreTrendData>> {
        StudentAcademicRepository::get_subject_trend_data(
            &self.pool,
            tenant_schema,
            student_id,
            date_range,
        ).await
    }

    /// 获取学生作业状态
    pub async fn get_homework_status(
        &self,
        tenant_schema: &str,
        student_id: Uuid,
        query_params: &StudentAcademicQuery,
    ) -> Result<Vec<StudentHomeworkRecord>> {
        StudentAcademicRepository::get_homework_records(
            &self.pool,
            tenant_schema,
            student_id,
            query_params,
        ).await
    }
}
