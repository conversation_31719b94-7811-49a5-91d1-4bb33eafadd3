import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
    TrendingUp,
    TrendingDown,
    Award,
    BookOpen,
    Target,
    Calendar,
    BarChart3,
    Clock
} from "lucide-react";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { toast } from "sonner";
import StudentAcademicApi, {
    StudentAcademicOverview, 
    StudentExamScore, 
    StudentSubjectPerformance, 
    StudentHomeworkRecord 
} from "@/services/studentAcademicApi";

// 使用API服务的类型定义
type ScoreOverview = StudentAcademicOverview;
type StudentScore = StudentExamScore;
type HomeworkStatus = StudentHomeworkRecord;
type SubjectTrend = StudentSubjectPerformance;

const StudentScoresDashboard: React.FC = () => {
    const [scoreOverview, setScoreOverview] = useState<ScoreOverview | null>(null);
    const [recentScores, setRecentScores] = useState<StudentScore[]>([]);
    const [subjectTrends, setSubjectTrends] = useState<SubjectTrend[]>([]);
    const [homeworkStatus, setHomeworkStatus] = useState<HomeworkStatus[]>([]);
    const [loading, setLoading] = useState(true);

    // 数据加载
    useEffect(() => {
        const loadData = async () => {
            try {
                setLoading(true);
                // 并行获取所有数据 - 使用新的自查接口
                const [overviewData, recentScoresData, subjectTrendsData, homeworkRecords] = await Promise.all([
                    StudentAcademicApi.getMyAcademicOverview().then(data => {
                        return data;
                    }).catch(err => {
                        throw err;
                    }),
                    StudentAcademicApi.getMyRecentScores(10).then(data => {
                        return data;
                    }).catch(err => {
                        throw err;
                    }),
                    StudentAcademicApi.getMySubjectTrends('90d').then(data => {
                        return data;
                    }).catch(err => {
                        throw err;
                    }),
                    StudentAcademicApi.getMyHomeworkRecords(10).then(data => {
                        return data;
                    }).catch(err => {
                        throw err;
                    })
                ]);

                // 设置概览数据
                setScoreOverview(overviewData);
                setRecentScores(recentScoresData);
                
                // 设置学科趋势数据
                setSubjectTrends(subjectTrendsData);
                
                // 设置作业状态数据
                setHomeworkStatus(homeworkRecords);

            } catch (error) {
                toast.error("数据加载失败", {
                    description: "无法获取学生学术数据，请检查网络连接后重试",
                });
            } finally {
                setLoading(false);
            }
        };

        loadData();
    }, []);

    const getTrendIcon = (trend: string) => {
        switch (trend) {
            case 'up':
                return <TrendingUp className="h-4 w-4 text-green-500" />;
            case 'down':
                return <TrendingDown className="h-4 w-4 text-red-500" />;
            default:
                return <BarChart3 className="h-4 w-4 text-gray-500" />;
        }
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'completed':
                return 'bg-green-100 text-green-800';
            case 'pending':
                return 'bg-yellow-100 text-yellow-800';
            case 'overdue':
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const getStatusText = (status: string) => {
        switch (status) {
            case 'completed':
                return '已完成';
            case 'pending':
                return '待完成';
            case 'overdue':
                return '已逾期';
            default:
                return '未知';
        }
    };

    if (loading) {
        return (
            <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    {[...Array(4)].map((_, i) => (
                        <Card key={i} className="animate-pulse">
                            <CardContent className="p-6">
                                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                            </CardContent>
                        </Card>
                    ))}
                </div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* 成绩概览卡片 */}
            {scoreOverview && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">平均分</p>
                                    <p className="text-2xl font-bold">{scoreOverview.average_score}</p>
                                </div>
                                <Award className="h-8 w-8 text-yellow-500" />
                            </div>
                            <div className="flex items-center space-x-2 text-xs text-muted-foreground mt-2">
                                {getTrendIcon(scoreOverview.improvement_trend)}
                                <span>{scoreOverview.improvement_rate > 0 ? '+' : ''}{scoreOverview.improvement_rate}%</span>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">班级排名</p>
                                    <p className="text-2xl font-bold">{scoreOverview.class_rank}</p>
                                </div>
                                <Target className="h-8 w-8 text-blue-500" />
                            </div>
                            <p className="text-xs text-muted-foreground mt-2">共参加 {scoreOverview.total_exams} 次考试</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">年级排名</p>
                                    <p className="text-2xl font-bold">{scoreOverview.grade_rank}</p>
                                </div>
                                <BarChart3 className="h-8 w-8 text-green-500" />
                            </div>
                            <p className="text-xs text-muted-foreground mt-2">持续进步中</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">作业完成率</p>
                                    <p className="text-2xl font-bold">{Math.round(scoreOverview.homework_completion_rate)}%</p>
                                </div>
                                <BookOpen className="h-8 w-8 text-purple-500" />
                            </div>
                            <Progress value={scoreOverview.homework_completion_rate} className="mt-2" />
                        </CardContent>
                    </Card>
                </div>
            )}

            {/* 详细内容区域 */}
            <Tabs defaultValue="recent-scores" className="space-y-4">
                <TabsList>
                    <TabsTrigger value="recent-scores">最近成绩</TabsTrigger>
                    <TabsTrigger value="subject-trends">学科趋势</TabsTrigger>
                    <TabsTrigger value="homework-status">作业状态</TabsTrigger>
                </TabsList>

                <TabsContent value="recent-scores" className="space-y-4">
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center space-x-2">
                                <Calendar className="h-5 w-5" />
                                <span>最近考试成绩</span>
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {recentScores?.length > 0 ? (
                                    recentScores?.map((score) => (
                                        <div key={score.id} className="flex items-center justify-between p-4 border rounded-lg">
                                            <div className="flex-1">
                                                <div className="flex items-center space-x-3">
                                                    <h4 className="font-medium">{score.subject_name}</h4>
                                                    <Badge variant="secondary">排名 {score.rank}</Badge>
                                                </div>
                                                <p className="text-sm text-muted-foreground mt-1">
                                                    {score.exam_name} · {new Date(score.exam_date).toLocaleDateString()}
                                                </p>
                                            </div>
                                            <div className="text-right">
                                                <p className="text-2xl font-bold">{score.score}</p>
                                                <p className="text-sm text-muted-foreground">/ {score.total_possible}</p>
                                            </div>
                                        </div>
                                    ))
                                ) : (
                                    <div className="text-center py-8 text-muted-foreground">
                                        <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
                                        <p>暂无考试成绩数据</p>
                                        <p className="text-sm">完成考试后成绩将在这里显示</p>
                                    </div>
                                )}
                            </div>
                        </CardContent>
                    </Card>
                </TabsContent>

                <TabsContent value="subject-trends" className="space-y-4">
                    {subjectTrends?.length > 0 ? (
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            {subjectTrends?.map((trend) => (
                                <Card key={trend.subject}>
                                    <CardHeader>
                                        <CardTitle className="flex items-center justify-between">
                                            <span>{trend.subject}成绩趋势</span>
                                            <div className="flex items-center space-x-2">
                                                {getTrendIcon(trend.trend)}
                                                <span className="text-sm text-muted-foreground">
                                                    最新: {trend.latest_score}分
                                                </span>
                                            </div>
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="h-64">
                                            <ResponsiveContainer width="100%" height="100%">
                                                <LineChart data={trend.data}>
                                                    <CartesianGrid strokeDasharray="3 3" />
                                                    <XAxis dataKey="date" />
                                                    <YAxis domain={[0, 100]} />
                                                    <Tooltip />
                                                    <Line
                                                        type="monotone"
                                                        dataKey="score"
                                                        stroke="#3b82f6"
                                                        strokeWidth={2}
                                                        dot={{ fill: '#3b82f6' }}
                                                    />
                                                </LineChart>
                                            </ResponsiveContainer>
                                        </div>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                    ) : (
                        <Card>
                            <CardContent className="py-12">
                                <div className="text-center text-muted-foreground">
                                    <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                                    <p>暂无学科趋势数据</p>
                                    <p className="text-sm">参加更多考试后将显示学科成绩趋势</p>
                                </div>
                            </CardContent>
                        </Card>
                    )}
                </TabsContent>

                <TabsContent value="homework-status" className="space-y-4">
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center space-x-2">
                                <Clock className="h-5 w-5" />
                                <span>作业完成情况</span>
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {homeworkStatus?.length > 0 ? (
                                    homeworkStatus?.map((homework) => (
                                        <div key={homework.id} className="flex items-center justify-between p-4 border rounded-lg">
                                            <div className="flex-1">
                                                <div className="flex items-center space-x-3">
                                                    <h4 className="font-medium">{homework.homework_name}</h4>
                                                    <Badge className={getStatusColor(homework.status)}>
                                                        {getStatusText(homework.status)}
                                                    </Badge>
                                                </div>
                                                <p className="text-sm text-muted-foreground mt-1">
                                                    {homework.subject_name} · 截止时间: {new Date(homework.due_date).toLocaleDateString()}
                                                </p>
                                            </div>
                                            <div className="text-right">
                                                {homework.score && homework.total_score && (
                                                    <>
                                                        <p className="text-lg font-bold">{homework.score}</p>
                                                        <p className="text-sm text-muted-foreground">/ {homework.total_score}</p>
                                                    </>
                                                )}
                                            </div>
                                        </div>
                                    ))
                                ) : (
                                    <div className="text-center py-8 text-muted-foreground">
                                        <Clock className="h-12 w-12 mx-auto mb-4 opacity-50" />
                                        <p>暂无作业数据</p>
                                        <p className="text-sm">老师布置作业后将在这里显示</p>
                                    </div>
                                )}
                            </div>
                        </CardContent>
                    </Card>
                </TabsContent>
            </Tabs>
        </div>
    );
};

export default StudentScoresDashboard; 