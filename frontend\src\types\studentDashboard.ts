// Student Dashboard Types for Deep-Mate frontend application

// 基础成绩信息
export interface BaseScore {
    id: string;
    score: number;
    total_possible: number;
    percentage: number;
    created_at: string;
    updated_at: string;
}

// 学生成绩概览
export interface StudentScoreOverview {
    total_exams: number;
    average_score: number;
    class_rank: number;
    grade_rank: number;
    improvement_trend: 'up' | 'down' | 'stable';
    improvement_rate: number;
    homework_completion_rate: number;
    total_homework_assigned: number;
    total_homework_completed: number;
    strongest_subject?: string;
    weakest_subject?: string;
    recent_achievements: string[];
}

// 考试成绩详情
export interface ExamScore extends BaseScore {
    exam_id: string;
    exam_name: string;
    exam_type: 'midterm' | 'final' | 'monthly' | 'quiz' | 'other';
    exam_date: string;
    subject_id: string;
    subject_name: string;
    class_rank: number;
    grade_rank: number;
    school_rank?: number;
    question_scores?: QuestionScore[];
    knowledge_point_scores?: KnowledgePointScore[];
}

// 题目得分详情
export interface QuestionScore {
    question_id: string;
    question_number: string;
    question_type: 'choice' | 'blank' | 'essay' | 'calculation';
    score: number;
    total_possible: number;
    is_correct: boolean;
    student_answer?: string;
    correct_answer?: string;
    knowledge_points: string[];
    difficulty_level: 'easy' | 'medium' | 'hard';
    time_spent?: number; // 秒
}

// 知识点得分
export interface KnowledgePointScore {
    knowledge_point: string;
    subject: string;
    score: number;
    total_possible: number;
    mastery_level: number; // 0-100
    question_count: number;
    correct_count: number;
}

// 学科成绩趋势
export interface SubjectScoreTrend {
    subject_id: string;
    subject_name: string;
    latest_score: number;
    latest_rank: number;
    trend: 'up' | 'down' | 'stable';
    improvement_rate: number;
    score_history: Array<{
        date: string;
        exam_id: string;
        exam_name: string;
        score: number;
        percentage: number;
        rank: number;
        class_average?: number;
        grade_average?: number;
    }>;
    performance_analysis: {
        consistency_score: number; // 稳定性评分
        peak_performance: number;
        lowest_performance: number;
        average_improvement: number;
    };
}

// 作业状态
export interface HomeworkStatus {
    id: string;
    homework_id: string;
    homework_name: string;
    subject_id: string;
    subject_name: string;
    assigned_date: string;
    due_date: string;
    submission_date?: string;
    status: 'not_submitted' | 'submitted' | 'graded' | 'overdue' | 'excused';
    score?: number;
    total_score?: number;
    percentage?: number;
    feedback?: string;
    teacher_comments?: string;
    submission_attempts: number;
    time_spent?: number; // 分钟
}

// 知识点掌握情况
export interface KnowledgePointMastery {
    point_id: string;
    point_name: string;
    subject: string;
    category: string;
    mastery_level: number; // 0-100
    confidence_level: number; // 0-100
    recent_practice_count: number;
    correct_rate: number;
    improvement_trend: 'up' | 'down' | 'stable';
    difficulty_level: 'easy' | 'medium' | 'hard';
    recommended_practice_time: number; // 分钟
    related_topics: string[];
    last_practiced_date?: string;
}

// 学科能力分析
export interface SubjectAbility {
    subject: string;
    overall_score: number;
    understanding: number; // 理解能力
    application: number;   // 应用能力
    analysis: number;      // 分析能力
    synthesis: number;     // 综合能力
    memory: number;        // 记忆能力
    problem_solving: number; // 解题能力
    creativity: number;    // 创新能力
}

// 学习建议
export interface StudyRecommendation {
    id: string;
    type: 'urgent' | 'important' | 'suggested' | 'optional';
    priority: number; // 1-5, 5最高
    subject: string;
    knowledge_point?: string;
    title: string;
    description: string;
    reasoning: string;
    estimated_time: number; // 分钟
    difficulty: 'easy' | 'medium' | 'hard';
    expected_improvement: number; // 预期提升百分比
    deadline?: string;
    resources: StudyResource[];
    progress: number; // 0-100, 完成进度
    status: 'pending' | 'in_progress' | 'completed' | 'skipped';
}

// 学习资源
export interface StudyResource {
    id: string;
    type: 'video' | 'exercise' | 'reading' | 'game' | 'practice_test';
    title: string;
    description?: string;
    url?: string;
    duration: number; // 分钟
    difficulty: 'easy' | 'medium' | 'hard';
    rating?: number; // 1-5星
    completion_rate?: number; // 0-100
    is_required: boolean;
}

// 进步轨迹
export interface ProgressTrack {
    date: string;
    overall_score: number;
    subject_scores: Record<string, number>;
    class_rank: number;
    grade_rank: number;
    milestone_achieved?: string;
    notes?: string;
    improvement_areas: string[];
    challenges: string[];
    teacher_feedback?: string;
}

// 学习统计
export interface LearningStatistics {
    total_study_time: number; // 分钟
    average_daily_study_time: number; // 分钟
    most_active_subject: string;
    most_improved_subject: string;
    consistency_score: number; // 0-100
    goal_completion_rate: number; // 0-100
    weekly_progress: Array<{
        week: string;
        study_time: number;
        scores_average: number;
        assignments_completed: number;
    }>;
}

// 同伴比较
export interface PeerComparison {
    my_rank: number;
    total_students: number;
    percentile: number;
    class_average: number;
    grade_average: number;
    school_average?: number;
    subjects_above_average: string[];
    subjects_below_average: string[];
    improvement_rank: number; // 进步排名
}

// 学习目标
export interface LearningGoal {
    id: string;
    title: string;
    description: string;
    subject?: string;
    target_score?: number;
    target_rank?: number;
    deadline: string;
    progress: number; // 0-100
    status: 'active' | 'completed' | 'paused' | 'cancelled';
    milestones: Array<{
        title: string;
        description: string;
        target_date: string;
        completed: boolean;
        completed_date?: string;
    }>;
    created_date: string;
    updated_date: string;
}

// 完整的学生看板数据
export interface StudentDashboardData {
    overview: StudentScoreOverview;
    recent_scores: ExamScore[];
    subject_trends: SubjectScoreTrend[];
    homework_status: HomeworkStatus[];
    knowledge_mastery: KnowledgePointMastery[];
    subject_abilities: SubjectAbility[];
    recommendations: StudyRecommendation[];
    progress_tracks: ProgressTrack[];
    learning_statistics: LearningStatistics;
    peer_comparison: PeerComparison;
    learning_goals: LearningGoal[];
    performance_insights: {
        strongest_subjects: string[];
        improvement_needed: string[];
        recent_achievements: string[];
        upcoming_deadlines: Array<{
            homework_id: string;
            homework_name: string;
            subject_name: string;
            due_date: string;
            days_remaining: number;
            priority: 'high' | 'medium' | 'low';
        }>;
        study_suggestions: string[];
        motivational_messages: string[];
    };
}

// API 响应类型
export interface StudentDashboardResponse {
    success: boolean;
    data: StudentDashboardData;
    message: string;
    timestamp: number;
}

// 查询参数
export interface StudentDashboardQuery {
    time_range?: '1week' | '1month' | '3months' | '6months' | '1year';
    subjects?: string[];
    include_peer_comparison?: boolean;
    include_recommendations?: boolean;
    detail_level?: 'basic' | 'detailed' | 'comprehensive';
}

// 类型已通过 export interface 声明导出，无需重复导出 