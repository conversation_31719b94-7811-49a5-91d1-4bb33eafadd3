use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;

#[derive(Debug, FromRow, Serialize, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub struct TenantQuestionAnswer {
    pub id: Uuid,
    pub question_id: Uuid,
    pub answer_area_id: String,
    pub content: String,
    pub explanation: Option<String>, // 解析
    pub updated_at: Option<DateTime<Utc>>,
    pub public_id: Option<Uuid>, // 来源id
}