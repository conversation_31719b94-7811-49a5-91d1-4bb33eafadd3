import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
    Brain,
    Target,
    TrendingUp,
    AlertCircle,
    CheckCircle,
    Clock,
    Award,
    Lightbulb,
    ArrowRight
} from "lucide-react";
import {
    RadarChart,
    PolarGrid,
    PolarAngleAxis,
    PolarRadiusAxis,
    Radar,
    ResponsiveContainer,
    LineChart,
    Line,
    XAxis,
    YAxis,
    CartesianGrid,
    Tooltip,
} from 'recharts';

import { StudentPerformanceApi, type KnowledgePointMastery, type SubjectAbility, type StudyRecommendation, type ProgressTrack, type StudentPerformanceQuery } from '@/services/studentPerformanceApi';

// Note: Interfaces are now imported from the API service

const StudentPerformanceAnalytics: React.FC = () => {
    const [knowledgePoints, setKnowledgePoints] = useState<KnowledgePointMastery[]>([]);
    const [subjectAbilities, setSubjectAbilities] = useState<SubjectAbility[]>([]);
    const [recommendations, setRecommendations] = useState<StudyRecommendation[]>([]);
    const [progressTracks, setProgressTracks] = useState<ProgressTrack[]>([]);
    const [loading, setLoading] = useState(true);
    const [selectedSubject, setSelectedSubject] = useState<string>('all');
    const [timeRange, setTimeRange] = useState<'30d' | '90d' | '180d' | '1y'>('90d');
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        const loadAnalyticsData = async () => {
            setLoading(true);
            setError(null);

            try {
                const queryParams: StudentPerformanceQuery = {
                    time_range: timeRange,
                    subject: selectedSubject === 'all' ? undefined : selectedSubject,
                };

                // 并行获取所有数据
                const [knowledgePointsData, subjectAbilitiesData, recommendationsData, progressTracksData] = await Promise.all([
                    StudentPerformanceApi.getKnowledgePointMastery(queryParams),
                    StudentPerformanceApi.getSubjectAbilities(queryParams),
                    StudentPerformanceApi.getStudyRecommendations(queryParams),
                    StudentPerformanceApi.getProgressTracks(queryParams),
                ]);

                setKnowledgePoints(knowledgePointsData || []);
                setSubjectAbilities(subjectAbilitiesData || []);
                setRecommendations(recommendationsData || []);
                setProgressTracks(progressTracksData || []);
            } catch (error) {
                console.error('获取学生表现分析数据失败:', error);
                setError('获取数据失败，请稍后重试');
                toast.error('获取学生表现分析数据失败');
                
                // 设置空数据以避免组件崩溃
                setKnowledgePoints([]);
                setSubjectAbilities([]);
                setRecommendations([]);
                setProgressTracks([]);
            } finally {
                setLoading(false);
            }
        };

        loadAnalyticsData();
    }, [timeRange, selectedSubject]);

    const getTrendIcon = (trend: 'up' | 'down' | 'stable') => {
        switch (trend) {
            case 'up':
                return <TrendingUp className="h-4 w-4 text-green-500" />;
            case 'down':
                return <AlertCircle className="h-4 w-4 text-red-500" />;
            default:
                return <CheckCircle className="h-4 w-4 text-gray-500" />;
        }
    };

    const getRecommendationType = (type: string) => {
        switch (type) {
            case 'urgent':
                return { color: 'bg-red-100 text-red-800', label: '紧急' };
            case 'important':
                return { color: 'bg-orange-100 text-orange-800', label: '重要' };
            default:
                return { color: 'bg-blue-100 text-blue-800', label: '建议' };
        }
    };

    const getDifficultyColor = (difficulty: string) => {
        switch (difficulty) {
            case 'easy':
                return 'text-green-600';
            case 'medium':
                return 'text-yellow-600';
            case 'hard':
                return 'text-red-600';
            default:
                return 'text-gray-600';
        }
    };

    const filteredKnowledgePoints = selectedSubject === 'all'
        ? knowledgePoints
        : knowledgePoints.filter(kp => kp.subject === selectedSubject);

    if (loading) {
        return (
            <div className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {[...Array(4)].map((_, i) => (
                        <Card key={i} className="animate-pulse">
                            <CardContent className="p-6">
                                <div className="h-64 bg-gray-200 rounded"></div>
                            </CardContent>
                        </Card>
                    ))}
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="space-y-6">
                <Card>
                    <CardContent className="p-6 text-center">
                        <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
                        <h3 className="text-lg font-semibold text-red-700 mb-2">数据加载失败</h3>
                        <p className="text-muted-foreground mb-4">{error}</p>
                        <Button onClick={() => window.location.reload()}>重新加载</Button>
                    </CardContent>
                </Card>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* 时间范围控制 */}
            <div className="flex items-center justify-between">
                <h2 className="text-2xl font-bold">学习表现分析</h2>
                <div className="flex space-x-2">
                    <span className="text-sm text-muted-foreground self-center">时间范围:</span>
                    {(['30d', '90d', '180d', '1y'] as const).map((range) => (
                        <Button
                            key={range}
                            variant={timeRange === range ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => setTimeRange(range)}
                        >
                            {range === '30d' ? '30天' : range === '90d' ? '90天' : range === '180d' ? '180天' : '1年'}
                        </Button>
                    ))}
                </div>
            </div>

            <Tabs defaultValue="knowledge-points" className="space-y-4">
                <TabsList className="grid w-full grid-cols-4">
                    <TabsTrigger value="knowledge-points">知识点分析</TabsTrigger>
                    <TabsTrigger value="ability-radar">能力雷达图</TabsTrigger>
                    <TabsTrigger value="recommendations">学习建议</TabsTrigger>
                    <TabsTrigger value="progress-track">进步轨迹</TabsTrigger>
                </TabsList>

                <TabsContent value="knowledge-points" className="space-y-4">
                    <div className="flex items-center justify-between">
                        <h3 className="text-lg font-semibold flex items-center space-x-2">
                            <Brain className="h-5 w-5" />
                            <span>知识点掌握情况</span>
                        </h3>
                        <div className="flex space-x-2">
                            <Button
                                variant={selectedSubject === 'all' ? 'default' : 'outline'}
                                size="sm"
                                onClick={() => setSelectedSubject('all')}
                            >
                                全部
                            </Button>
                            {Array.from(new Set((knowledgePoints || []).map(kp => kp.subject))).map(subject => (
                                <Button
                                    key={subject}
                                    variant={selectedSubject === subject ? 'default' : 'outline'}
                                    size="sm"
                                    onClick={() => setSelectedSubject(subject)}
                                >
                                    {subject}
                                </Button>
                            ))}
                        </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {filteredKnowledgePoints.length === 0 ? (
                            <div className="col-span-full text-center py-12">
                                <Brain className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                <h3 className="text-lg font-semibold text-muted-foreground mb-2">暂无知识点数据</h3>
                                <p className="text-sm text-muted-foreground">
                                    {selectedSubject === 'all' ? '当前时间范围内没有作业数据' : `${selectedSubject}学科暂无作业数据`}
                                </p>
                            </div>
                        ) : (
                            filteredKnowledgePoints.map((kp, index) => (
                            <Card key={index}>
                                <CardContent className="p-6">
                                    <div className="flex items-center justify-between mb-4">
                                        <div>
                                            <h4 className="font-medium">{kp.point_name}</h4>
                                            <p className="text-sm text-muted-foreground">{kp.subject}</p>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            {getTrendIcon(kp.improvement_trend)}
                                            <Badge variant="secondary">
                                                <span className={getDifficultyColor(kp.difficulty_level)}>
                                                    {kp.difficulty_level === 'easy' ? '简单' :
                                                        kp.difficulty_level === 'medium' ? '中等' : '困难'}
                                                </span>
                                            </Badge>
                                        </div>
                                    </div>

                                    <div className="space-y-3">
                                        <div>
                                            <div className="flex justify-between text-sm mb-1">
                                                <span>掌握程度</span>
                                                <span>{kp.mastery_level}%</span>
                                            </div>
                                            <Progress value={kp.mastery_level} className="h-2" />
                                        </div>

                                        <div className="flex justify-between text-sm text-muted-foreground">
                                            <span>最近练习: {kp.recent_practice_count}次</span>
                                            <span>建议练习: {kp.recommended_practice_time}分钟</span>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                            ))
                        )}
                    </div>
                </TabsContent>

                <TabsContent value="ability-radar" className="space-y-4">
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center space-x-2">
                                <Target className="h-5 w-5" />
                                <span>学科能力雷达图</span>
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            {subjectAbilities.length === 0 ? (
                                <div className="text-center py-12">
                                    <Target className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                    <h3 className="text-lg font-semibold text-muted-foreground mb-2">暂无学科能力数据</h3>
                                    <p className="text-sm text-muted-foreground">当前时间范围内没有足够的作业数据来分析学科能力</p>
                                </div>
                            ) : (
                                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                    {subjectAbilities.map((ability) => (
                                    <div key={ability.subject} className="space-y-4">
                                        <h4 className="text-center font-medium">{ability.subject}</h4>
                                        <div className="h-64">
                                            <ResponsiveContainer width="100%" height="100%">
                                                <RadarChart data={[
                                                    { ability: '理解', value: ability.understanding },
                                                    { ability: '应用', value: ability.application },
                                                    { ability: '分析', value: ability.analysis },
                                                    { ability: '综合', value: ability.synthesis },
                                                    { ability: '记忆', value: ability.memory }
                                                ]}>
                                                    <PolarGrid />
                                                    <PolarAngleAxis dataKey="ability" />
                                                    <PolarRadiusAxis domain={[0, 100]} />
                                                    <Radar
                                                        name={ability.subject}
                                                        dataKey="value"
                                                        stroke="#3b82f6"
                                                        fill="#3b82f6"
                                                        fillOpacity={0.3}
                                                    />
                                                </RadarChart>
                                            </ResponsiveContainer>
                                        </div>
                                    </div>
                                    ))}
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </TabsContent>

                <TabsContent value="recommendations" className="space-y-4">
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center space-x-2">
                                <Lightbulb className="h-5 w-5" />
                                <span>个性化学习建议</span>
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            {recommendations.length === 0 ? (
                                <div className="text-center py-12">
                                    <Lightbulb className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                    <h3 className="text-lg font-semibold text-muted-foreground mb-2">暂无学习建议</h3>
                                    <p className="text-sm text-muted-foreground">当前时间范围内没有足够的作业数据来生成个性化学习建议</p>
                                </div>
                            ) : (
                                <div className="space-y-4">
                                    {recommendations
                                        .sort((a, b) => b.priority - a.priority)
                                        .map((rec) => {
                                        const typeInfo = getRecommendationType(rec.type);
                                        return (
                                            <div key={rec.id} className="border rounded-lg p-4">
                                                <div className="flex items-start justify-between mb-3">
                                                    <div className="flex-1">
                                                        <div className="flex items-center space-x-3 mb-2">
                                                            <h4 className="font-medium">{rec.title}</h4>
                                                            <Badge className={typeInfo.color}>
                                                                {typeInfo.label}
                                                            </Badge>
                                                            <Badge variant="outline">{rec.subject}</Badge>
                                                        </div>
                                                        <p className="text-sm text-muted-foreground mb-3">
                                                            {rec.description}
                                                        </p>
                                                        <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                                                            <span className="flex items-center space-x-1">
                                                                <Clock className="h-3 w-3" />
                                                                <span>{rec.estimated_time}分钟</span>
                                                            </span>
                                                            <span className={getDifficultyColor(rec.difficulty)}>
                                                                {rec.difficulty === 'easy' ? '简单' :
                                                                    rec.difficulty === 'medium' ? '中等' : '困难'}
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <Button size="sm" className="ml-4">
                                                        开始学习
                                                        <ArrowRight className="h-3 w-3 ml-1" />
                                                    </Button>
                                                </div>

                                                <div className="space-y-2">
                                                    <p className="text-sm font-medium">推荐资源:</p>
                                                    <div className="flex flex-wrap gap-2">
                                                        {rec.resources.map((resource, idx) => (
                                                            <Badge key={idx} variant="secondary" className="text-xs">
                                                                {resource.type === 'video' && '📹'}
                                                                {resource.type === 'exercise' && '📝'}
                                                                {resource.type === 'reading' && '📚'}
                                                                {resource.title} ({resource.duration}分钟)
                                                            </Badge>
                                                        ))}
                                                    </div>
                                                </div>
                                            </div>
                                        );
                                        })}
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </TabsContent>

                <TabsContent value="progress-track" className="space-y-4">
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center space-x-2">
                                <Award className="h-5 w-5" />
                                <span>学习进步轨迹</span>
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            {progressTracks.length === 0 ? (
                                <div className="text-center py-12">
                                    <Award className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                    <h3 className="text-lg font-semibold text-muted-foreground mb-2">暂无进步轨迹数据</h3>
                                    <p className="text-sm text-muted-foreground">当前时间范围内没有足够的作业数据来分析学习进步轨迹</p>
                                </div>
                            ) : (
                                <>
                                    <div className="h-80 mb-6">
                                        <ResponsiveContainer width="100%" height="100%">
                                            <LineChart data={[...progressTracks].reverse()}>
                                                <CartesianGrid strokeDasharray="3 3" />
                                                <XAxis dataKey="date" />
                                                <YAxis domain={[0, 100]} />
                                                <Tooltip />
                                                <Line
                                                    type="monotone"
                                                    dataKey="overall_score"
                                                    stroke="#3b82f6"
                                                    strokeWidth={2}
                                                    name="总分"
                                                />
                                            </LineChart>
                                        </ResponsiveContainer>
                                    </div>

                                    <div className="space-y-4">
                                        <h4 className="font-medium">重要里程碑</h4>
                                        {progressTracks
                                            .filter(track => track.milestone_achieved)
                                            .map((track, index) => (
                                                <div key={index} className="flex items-center space-x-3 p-3 bg-green-50 rounded-lg">
                                                    <Award className="h-5 w-5 text-green-600" />
                                                    <div>
                                                        <p className="font-medium text-green-800">{track.milestone_achieved}</p>
                                                        <p className="text-sm text-green-600">{track.date}</p>
                                                    </div>
                                                </div>
                                            ))}
                                    </div>
                                </>
                            )}
                        </CardContent>
                    </Card>
                </TabsContent>
            </Tabs>
        </div>
    );
};

export default StudentPerformanceAnalytics; 