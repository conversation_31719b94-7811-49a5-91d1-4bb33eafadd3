/**
 * 头像工具函数
 * 支持网络头像URL和基于用户名自动生成头像
 */

/**
 * 生成基于用户名的头像颜色
 * @param username 用户名
 * @returns 头像背景色和文字颜色
 */
export function generateAvatarColors(username: string): {
    backgroundColor: string;
    textColor: string;
} {
    if (!username) {
        return {
            backgroundColor: '#6366f1', // 默认紫色
            textColor: '#ffffff'
        };
    }

    // 基于用户名生成稳定的颜色
    let hash = 0;
    for (let i = 0; i < username.length; i++) {
        hash = username.charCodeAt(i) + ((hash << 5) - hash);
    }

    // 定义一组美观的颜色方案
    const colorSchemes = [
        { bg: '#ef4444', text: '#ffffff' }, // 红色
        { bg: '#f97316', text: '#ffffff' }, // 橙色
        { bg: '#eab308', text: '#ffffff' }, // 黄色
        { bg: '#22c55e', text: '#ffffff' }, // 绿色
        { bg: '#06b6d4', text: '#ffffff' }, // 青色
        { bg: '#3b82f6', text: '#ffffff' }, // 蓝色
        { bg: '#6366f1', text: '#ffffff' }, // 紫色
        { bg: '#8b5cf6', text: '#ffffff' }, // 紫罗兰
        { bg: '#ec4899', text: '#ffffff' }, // 粉色
        { bg: '#14b8a6', text: '#ffffff' }, // 蓝绿色
    ];

    const index = Math.abs(hash) % colorSchemes.length;
    const scheme = colorSchemes[index];

    return {
        backgroundColor: scheme.bg,
        textColor: scheme.text
    };
}

/**
 * 获取用户名的首字母（支持中文）
 * @param username 用户名
 * @returns 首字母或首个字符
 */
export function getAvatarInitial(username: string): string {
    if (!username) return '?';

    // 去除空格并取第一个字符
    const firstChar = username.trim().charAt(0).toUpperCase();

    // 如果是中文字符，直接返回
    if (/[\u4e00-\u9fff]/.test(firstChar)) {
        return firstChar;
    }

    // 如果是英文字符，返回大写
    if (/[a-zA-Z]/.test(firstChar)) {
        return firstChar;
    }

    // 其他情况返回第一个字符
    return firstChar || '?';
}

/**
 * 检查是否为有效的头像URL
 * @param url 头像URL
 * @returns 是否为有效URL
 */
export function isValidAvatarUrl(url?: string | null): boolean {
    if (!url || url.trim() === '') return false;

    try {
        const urlObj = new URL(url);
        // 检查是否为http或https协议
        return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
    } catch {
        return false;
    }
}

/**
 * 生成头像props
 * @param username 用户名
 * @param avatarUrl 头像URL（可选，如果无效则使用生成的头像）
 * @returns 头像组件所需的props
 */
export function generateAvatarProps(username: string, avatarUrl?: string | null): {
    src?: string;
    fallbackText: string;
    fallbackStyle: React.CSSProperties;
} {
    const initial = getAvatarInitial(username);
    const colors = generateAvatarColors(username);

    return {
        src: isValidAvatarUrl(avatarUrl) ? avatarUrl! : undefined,
        fallbackText: initial,
        fallbackStyle: {
            backgroundColor: colors.backgroundColor,
            color: colors.textColor,
            fontWeight: '600',
            fontSize: '0.875rem'
        }
    };
}

