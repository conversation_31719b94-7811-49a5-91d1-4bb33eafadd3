import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { X } from 'lucide-react'
import { HomeworkFeedbackStatus } from '@/services/studentScoreApi'

// 演示组件，展示修复后的状态标签和悬停功能
const FeedbackStatusDemo: React.FC = () => {
  const [popoverOpen, setPopoverOpen] = useState<string | null>(null)

  // 获取状态标签配置
  const getStatusBadgeConfig = (status: HomeworkFeedbackStatus) => {
    const configs = {
      Initial: {
        variant: 'default' as const,
        label: '已提交',
        className: 'bg-blue-100 text-blue-800 hover:bg-blue-200 border-blue-200',
      },
      Received: {
        variant: 'default' as const,
        label: '已处理',
        className: 'bg-green-100 text-green-800 hover:bg-green-200 border-green-200',
      },
      Rejected: {
        variant: 'destructive' as const,
        label: '已拒绝',
        className: 'bg-red-100 text-red-800 hover:bg-red-200 border-red-200',
      },
      Cancelled: {
        variant: 'secondary' as const,
        label: '已关闭',
        className: 'bg-gray-100 text-gray-800 hover:bg-gray-200 border-gray-200',
      },
      Resubmitted: {
        variant: 'outline' as const,
        label: '重新打开',
        className: 'bg-orange-100 text-orange-800 hover:bg-orange-200 border-orange-200',
      },
    }
    return configs[status] || configs.Initial
  }

  const statuses: HomeworkFeedbackStatus[] = ['Initial', 'Received', 'Rejected', 'Cancelled', 'Resubmitted']

  const handleRejectClick = (status: string) => {
    alert(`拒绝反馈: ${status}`)
    setPopoverOpen(null)
  }

  return (
    <div className="p-8 space-y-6">
      <h2 className="text-2xl font-bold mb-4">反馈状态标签演示</h2>
      
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">所有状态标签：</h3>
        <div className="flex flex-wrap gap-4">
          {statuses.map((status) => (
            <div key={status} className="flex flex-col items-center space-y-2">
              <Badge 
                className={getStatusBadgeConfig(status).className} 
                variant={getStatusBadgeConfig(status).variant}
              >
                {getStatusBadgeConfig(status).label}
              </Badge>
              <span className="text-xs text-gray-500">{status}</span>
            </div>
          ))}
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">悬停交互演示（仅"已提交"状态）：</h3>
        <div className="flex gap-4">
          {/* 已提交状态 - 支持悬停显示拒绝按钮 */}
          <div className="flex flex-col items-center space-y-2">
            <Popover open={popoverOpen === 'demo-initial'} onOpenChange={(open) => setPopoverOpen(open ? 'demo-initial' : null)}>
              <PopoverTrigger asChild>
                <Badge 
                  className={`cursor-pointer ${getStatusBadgeConfig('Initial').className}`} 
                  variant={getStatusBadgeConfig('Initial').variant}
                  onMouseEnter={() => setPopoverOpen('demo-initial')}
                  onMouseLeave={() => setPopoverOpen(null)}
                >
                  {getStatusBadgeConfig('Initial').label}
                </Badge>
              </PopoverTrigger>
              <PopoverContent className='w-auto p-2' side="top" align="center">
                <Button 
                  size='sm' 
                  variant='destructive' 
                  onClick={() => handleRejectClick('Initial')} 
                  className='h-8 px-3 text-xs'
                >
                  <X className='h-3 w-3 mr-1' />
                  拒绝反馈
                </Button>
              </PopoverContent>
            </Popover>
            <span className="text-xs text-gray-500">悬停试试</span>
          </div>

          {/* 其他状态 - 不支持悬停 */}
          <div className="flex flex-col items-center space-y-2">
            <Badge 
              className={getStatusBadgeConfig('Received').className} 
              variant={getStatusBadgeConfig('Received').variant}
            >
              {getStatusBadgeConfig('Received').label}
            </Badge>
            <span className="text-xs text-gray-500">无悬停效果</span>
          </div>
        </div>
      </div>

      <div className="mt-8 p-4 bg-gray-50 rounded-lg">
        <h4 className="font-semibold mb-2">功能说明：</h4>
        <ul className="text-sm space-y-1 text-gray-700">
          <li>• 不同状态使用不同颜色的标签</li>
          <li>• 只有"已提交"状态支持鼠标悬停显示拒绝按钮</li>
          <li>• 悬停框显示在标签上方，避免遮挡内容</li>
          <li>• 点击拒绝按钮会触发确认对话框</li>
          <li>• 使用 Popover 组件实现悬停效果</li>
        </ul>
      </div>
    </div>
  )
}

export default FeedbackStatusDemo
