import React from 'react'
import Mathdown from '@/components/question-card/v0.0.0/mathdown/Mathdown'

interface QuestionItemProps {
  question: any
  questionNumber: string
  answerAreaDetails: any[]
  showAnswers: boolean
}

const QuestionItem: React.FC<QuestionItemProps> = ({ question, questionNumber, answerAreaDetails, showAnswers }) => {
  // 渲染题目内容
  const renderQuestionContent = () => {
    if (!question?.items) return null

    return question.items.map((item: any, index: number) => {
      if (item.key === 'content') {
        // 处理content字段，可能直接在value中，也可能在value.content中
        const content = item.value?.content || item.value
        if (content) {
          return (
            <div key={index} className='mb-4'>
              <Mathdown content={content} />
            </div>
          )
        }
      }

      if (item.key === 'choices') {
        // 处理choices字段，可能直接在value中，也可能在value.choices中
        const choices = item.value?.choices || item.value
        if (choices && Array.isArray(choices) && choices.length > 0) {
          return (
            <div key={index} className='mb-4'>
              <div className='space-y-2'>
                {choices.map((choice: string, choiceIndex: number) => (
                  <div key={choiceIndex} className='flex items-start'>
                    <span className='font-semibold mr-2 min-w-[20px]'>{String.fromCharCode(65 + choiceIndex)}.</span>
                    <div className='flex-1'>
                      <Mathdown content={choice} />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )
        }
      }

      return null
    })
  }

  // 渲染答案
  const renderAnswers = () => {
    if (!showAnswers || !answerAreaDetails || answerAreaDetails.length === 0) {
      return null
    }

    return (
      <div className='mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg'>
        <h4 className='font-semibold text-blue-800 mb-2'>答案解析</h4>
        {answerAreaDetails.map((detail: any, detailIndex: number) => (
          <div key={detailIndex} className='mb-3'>
            {detail.answers?.map((answer: any, answerIndex: number) => (
              <div key={answerIndex} className='space-y-2'>
                {answer.content && (
                  <div>
                    <span className='font-medium text-blue-700'>答案：</span>
                    <Mathdown content={answer.content} />
                  </div>
                )}
                {answer.explanation && (
                  <div>
                    <span className='font-medium text-blue-700'>解析：</span>
                    <Mathdown content={answer.explanation} />
                  </div>
                )}
              </div>
            ))}
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className='mb-6 p-4 border border-gray-200 rounded-lg bg-white shadow-sm hover:shadow-md transition-shadow'>
      {/* 题号 */}
      <div className='flex items-start mb-3'>
        <span className='font-bold text-lg mr-3 min-w-[30px]'>{questionNumber}.</span>
        <div className='flex-1'>{renderQuestionContent()}</div>
      </div>

      {/* 答案区域 */}
      {renderAnswers()}
    </div>
  )
}

export default QuestionItem
