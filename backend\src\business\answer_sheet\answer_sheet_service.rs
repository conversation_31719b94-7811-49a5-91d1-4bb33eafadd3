use serde::{Deserialize, Serialize};
use serde_json::Value;
use sqlx::PgPool;
use uuid::Uuid;

use crate::business::{
    answer_sheet::answer_sheet_repository::{self, AnswerSheetModel, AnswerSheetRepository},
    section_answer_sheet::section_answer_sheet_repository::SectionAnswerSheetRepository,
};

/**
 * 作者：张瀚
 * 说明：答题卡服务
 */
pub struct AnswerSheetService {}

impl AnswerSheetService {
    /**
     * 作者：张瀚
     * 说明：新建答题卡数据
     */
    pub async fn create_answer_sheet(db: &PgPool, params: InsertParams) -> Result<AnswerSheetModel, String> {
        let InsertParams {
            section_id,
            data_version,
            render_data,
            annotation,
        } = params;
        //取出需要保存的部分
        let db_params = answer_sheet_repository::InsertParams {
            data_version,
            render_data,
            annotation,
        };
        let answer_bean = AnswerSheetRepository::insert(db, db_params).await?;
        // 用新的ID保存关联关系
        SectionAnswerSheetRepository::bind_section_and_answer_sheet(db, &section_id, &answer_bean.id).await?;
        Ok(answer_bean)
    }
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub struct InsertParams {
    ///关联的内容块ID
    pub section_id: Uuid,
    pub data_version: String,
    ///渲染数据,根据版本,1.0.0版本使用的是RenderData
    pub render_data: Value,
    ///渲染数据,根据版本,1.0.0版本使用的是Annotation
    pub annotation: Option<Value>,
}
