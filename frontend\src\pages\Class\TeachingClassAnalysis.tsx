import React, { useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useNavigate, useParams } from 'react-router-dom';
import { Line, LineChart, ResponsiveContainer, Tooltip, YAxis } from 'recharts';
import { ArrowLeft, BookOpenText, Users } from 'lucide-react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { TeachingClassesApi } from '@/services/teachingClassesApi';
import { getTenantInfoFromLocalStorage } from '@/lib/apiUtils';
import { ClassHomeworksInfo, ClassHomeworkStatistics, StudentHomeworkAnalysis } from '@/types/teachingClasses';

const getStatusBadge = (status: string) => {
    const variants = {
        Draft: { variant: 'secondary', label: '草稿', color: 'bg-gray-50 text-gray-700 border-gray-200' },
        Doing: { variant: 'default', label: '进行中', color: 'bg-blue-50 text-blue-700 border-blue-200' },
        Done: { variant: 'outline', label: '已完成', color: 'bg-green-50 text-green-700 border-green-200' },
    };
    const config = variants[status as keyof typeof variants];
    return <Badge variant={config.variant as any} className={config.color}>{config.label}</Badge>;
};

const HomeworkCard: React.FC<{ hw: ClassHomeworksInfo; onClick: () => void }> = ({ hw, onClick }) => (
    <Card className="mb-4 cursor-pointer hover:shadow-md transition-shadow" onClick={onClick}>
        <CardHeader className="pb-2 flex flex-row items-center">
            <div className="p-2 rounded-lg bg-blue-50 text-blue-600">
                <BookOpenText className="h-4 w-4" />
            </div>
            <CardTitle className="text-base font-semibold ml-2 line-clamp-1">{hw.homework.homework_name}</CardTitle>
        </CardHeader>
        <CardContent className="flex flex-wrap justify-between text-sm text-muted-foreground">
            <div>
                <div>缺考人数: <span className="font-bold text-red-400">{hw.report.absent_student_count}</span></div>
                <div>异常学生: <span className="font-bold text-yellow-500">{hw.report.error_student_count}</span></div>
                <div className="mt-2">{getStatusBadge(hw.homework.homework_status)}</div>
            </div>
            <div>
                <div>总分: <span className="font-bold text-blue-400">100</span></div>
                <div>平均分: <span className="font-bold text-blue-400">{hw.report.avg_score}</span></div>
                <div className="flex items-center space-x-1 text-muted-foreground mt-2">
                    <Users className="h-3 w-3" />
                    <span>{hw.report.absent_student_count+hw.report.done_score_count+hw.report.error_student_count}</span>
                </div>
            </div>
        </CardContent>
    </Card>
);

const TeachingClassAnalysis: React.FC = () => {
    const navigate = useNavigate();
    const { classId } = useParams<{ classId: string }>();
    // Get tenant ID from auth context (mock for now)
    const identityInfo = getTenantInfoFromLocalStorage();
    const tenantId = identityInfo?.tenant_id || '';
    const tenantName = identityInfo?.schema_name || '';

    const [homeworkList, setHomeworkList] = React.useState<ClassHomeworksInfo[]>([]);
    const [studentListInfo, setStudentListInfo] = React.useState<StudentHomeworkAnalysis[]>([]);
    const [homeworkStatistics, setHomeworkStatistics] = React.useState<ClassHomeworkStatistics | null>(null);
    useEffect(() => {
        loadhomeworkList();
        loadStudentList();
        loadHomeworkStatistics();
    }, [classId]);
    const loadhomeworkList = () => {
        if (!classId) return;
        TeachingClassesApi.getClassHomeworkInfo(tenantId, tenantName, classId).then(res => {
            const { data, success, message } = res;
            if (!success) {
                console.error('获取作业列表失败:', message);
                return;
            }
            setHomeworkList(data || []);
        });
    };
    const loadStudentList = () => {
        if (!classId) return;
        TeachingClassesApi.getClassStudentHomeworkStatus(tenantId, tenantName, classId).then(res => {
            const { data, success, message } = res;
            if (!success) {
                console.error('获取学生列表失败:', message);
                return;
            }
            setStudentListInfo(data || []);
            console.log('获取学生列表成功:', data);
        });
    };
    const loadHomeworkStatistics = () => {
        if (!classId) return;
        TeachingClassesApi.getClassHomeworkStatistics(tenantId, tenantName, classId).then(res => {
            const { data, success, message } = res;
            if (!success) {
                console.error('获取作业统计信息失败:', message);
                return;
            }
            setHomeworkStatistics(data||null);
            console.log('获取作业统计信息成功:', data);
        });
    };
    //学生状态
    const getStatusBadge = (status: string) => {
        switch (status?.toLowerCase()) {
            case 'active':
                return <Badge variant="default">在校</Badge>;
            case 'inactive':
                return <Badge variant="secondary">休学</Badge>;
            case 'graduated':
                return <Badge variant="outline">毕业</Badge>;
            case 'transferred':
                return <Badge variant="destructive">转学</Badge>;
            default:
                return <Badge variant="secondary">未知</Badge>;
        }
    };
    return (
        <div className="space-y-6">
            <Button variant="outline" onClick={() => navigate('/teaching-classes')}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                返回
            </Button>
            <div className="grid grid-cols-1 xl:grid-cols-5 gap-6">
                {/* 左侧作业卡片列表 */}
                <Card className="xl:col-span-3">
                    <CardHeader>
                        <CardTitle>作业信息</CardTitle>
                        <CardDescription>该教学班的最近几次作业</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="grid md:grid-cols-2 gap-4 lg:grid-cols-3">
                            {homeworkList.map(hw => (
                                <HomeworkCard
                                    key={hw.homework.id.toString()}
                                    hw={hw}
                                    onClick={() => navigate(`/homework-setting/${hw.homework.id.toString()}`)}
                                />
                            ))}
                        </div>
                    </CardContent>
                </Card>
                {/* 右侧卡片 */}
                <Card className="xl:col-span-2">
                    <CardHeader>
                        <CardTitle>作业统计分析</CardTitle>
                        <CardDescription>展示近一个月作业统计信息</CardDescription>
                    </CardHeader>
                    <CardContent>
                        {/* 4个统计小卡片 */}
                        <div className="grid grid-cols-2 md:grid-cols-2 gap-4">
                            <Card className="shadow-none border bg-blue-50">
                                <CardHeader className="pb-2 flex flex-row items-center">
                                    <span className="p-1 rounded text-blue-600 mr-2">进行中</span>
                                    <CardTitle className="text-sm font-semibold"></CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <span className="font-bold text-blue-600 text-xl">{homeworkStatistics?.doing_homework_count}</span>
                                    <CardDescription className="mt-1 text-xs text-muted-foreground">当前未截止的作业</CardDescription>
                                </CardContent>
                            </Card>
                            <Card className="shadow-none border bg-green-50">
                                <CardHeader className="pb-2 flex flex-row items-center">
                                    <span className="p-1 rounded text-green-600 mr-2">已完成</span>
                                    <CardTitle className="text-sm font-semibold"></CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <span className="font-bold text-green-600 text-xl">{homeworkStatistics?.done_homework_count}</span>
                                    <CardDescription className="mt-1 text-xs text-muted-foreground">已截止并完成的作业</CardDescription>
                                </CardContent>
                            </Card>
                            <Card className="shadow-none border bg-indigo-50">
                                <CardHeader className="pb-2 flex flex-row items-center">
                                    <span className="p-1 rounded text-indigo-600 mr-2">学生缺勤率</span>
                                    <CardTitle className="text-sm font-semibold"></CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <span className="font-bold text-indigo-600 text-xl">{homeworkStatistics?.student_participation_rate.toFixed(2)}%</span>
                                    <CardDescription className="mt-1 text-xs text-muted-foreground">学生完成作业的比例</CardDescription>
                                </CardContent>
                            </Card>
                            <Card className="shadow-none border bg-purple-50">
                                <CardHeader className="pb-2 flex flex-row items-center">
                                    <span className="p-1 rounded text-purple-600 mr-2">及格率</span>
                                    <CardTitle className="text-sm font-semibold"></CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <span className="font-bold text-purple-600 text-xl">{homeworkStatistics?.student_pass_rate.toFixed(2)}%</span>
                                    <CardDescription className="mt-1 text-xs text-muted-foreground">作业分数及格的比例</CardDescription>
                                </CardContent>
                            </Card>
                        </div>
                    </CardContent>
                </Card>
            </div>
            <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                    <div>
                        <CardTitle>学生列表</CardTitle>
                        <CardDescription>学生作业完成情况</CardDescription>
                    </div>
                    <div>

                    </div>
                </CardHeader>
                <CardContent>
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>学生姓名</TableHead>
                                <TableHead>学号</TableHead>
                                <TableHead>状态</TableHead>
                                <TableHead>缺考次数</TableHead>
                                <TableHead>作业出勤率</TableHead>
                                <TableHead>查阅成绩次数</TableHead>
                                <TableHead>名次变化趋势</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {studentListInfo.map((studentInfo) => (
                                <TableRow key={studentInfo.student.id.toString()}>
                                    <TableCell>{studentInfo.student.student_name}</TableCell>
                                    <TableCell>{studentInfo.student.student_number}</TableCell>
                                    <TableCell>{getStatusBadge(studentInfo.student.status.toString())}</TableCell>
                                    <TableCell>{studentInfo.absent_homework_count}</TableCell>
                                    <TableCell>{
                                        studentInfo.done_homework_count + studentInfo.absent_homework_count===0?'-':
                                        ((studentInfo.done_homework_count / (studentInfo.done_homework_count + studentInfo.absent_homework_count)) * 100+'%')
                                    }</TableCell>
                                    <TableCell>{studentInfo.read_grade_count}</TableCell>
                                    <TableCell>
                                        <div style={{ width: 120, height: 60 }}>
                                            <ResponsiveContainer width="100%" height="100%">
                                                <LineChart data={studentInfo.ranks.map((rankInfo) => ({
                                                    rank: rankInfo === 0 ? null : rankInfo,
                                                }))}>
                                                    <Line type="monotone" dataKey="rank" stroke="#3b82f6" dot={false} strokeWidth={2} />
                                                    <YAxis
                                                        domain={[0, studentListInfo.length]}
                                                        reversed={true} // 反转Y轴
                                                        width={0} //仅保留刻度空间
                                                    />
                                                    <Tooltip
                                                        content={({ active, payload }) => {
                                                            if (active && payload && payload.length) {
                                                                return (
                                                                    <div className="rounded-lg border bg-background p-2 shadow-sm">
                                                                        <div className="grid grid-cols-2 gap-2">
                                                                            <div className="flex flex-col">
                                                                                <span className="text-[0.70rem] uppercase text-muted-foreground">
                                                                                    排名
                                                                                </span>
                                                                                <span className="font-bold text-muted-foreground">
                                                                                    {payload[0].value === null ? '缺考' : payload[0].value}
                                                                                </span>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                );
                                                            }
                                                            return null;
                                                        }}
                                                    />
                                                </LineChart>
                                            </ResponsiveContainer>
                                        </div>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </CardContent>
            </Card>
        </div>
    );
};

export default TeachingClassAnalysis;