import {
  AnswerAreaValueKeyEnum,
  ChoiceDataVO,
  ContentDataVO,
  getAnswerAreaValueKeyEnumOptions,
  IllustrationDataVO,
  PaperContentVO,
  PaperQuestionVO,
  QuestionGroupDataVO,
  QuestionUnitKeyEnum,
  QuestionUnitVO,
} from '@/components/question-card/v1.0.0/entity/paper-vo'
import { ComponentDataListStoreContext } from '@/components/question-card/v1.0.0/QuestionCard'
import { PaperDataStoreContext } from '@/components/question-card/v1.0.0/store/paperDataStore'
import { QuestionCardUI } from '@/components/question-card/v1.0.0/ui/question-card-components/global'
import { useCallback, useContext, useRef } from 'react'
import { v4 } from 'uuid'
import { useStore } from 'zustand'
import styles from '../QuestionNavigation.module.scss'

export interface AddQuestionBlockProps {
  /**
   * 所属的题组数据
   */
  questionGroup: QuestionGroupDataVO
  paperContent: PaperContentVO
  paperContentIndex: number
}
/**
 * 作者：张瀚
 * 说明：新增题目的块
 */
export function AddQuestionBlock({ questionGroup, paperContent, paperContentIndex }: AddQuestionBlockProps) {
  const useComponentDataListStoreContext = useContext(ComponentDataListStoreContext)
  const setNowAddingQuestionQuestionGroup = useStore(useComponentDataListStoreContext, (state) => state.setNowAddingQuestionInfo)
  function onClickHandler() {
    setNowAddingQuestionQuestionGroup({ questionGroup, paperContent, paperContentIndex })
  }
  return (
    <>
      <div className={`${styles['question-block']} ${styles['add']}`} title={`新增题目`} onClick={onClickHandler}>
        +
      </div>
    </>
  )
}

/**
 * 作者：张瀚
 * 说明：新增题目的弹窗
 */
export function AddQuestionDialog() {
  const useComponentDataListStoreContext = useContext(ComponentDataListStoreContext)
  const nowAddingQuestionInfo = useStore(useComponentDataListStoreContext, (state) => state.nowAddingQuestionInfo)
  const setNowAddingQuestionInfo = useStore(useComponentDataListStoreContext, (state) => state.setNowAddingQuestionInfo)
  const bucketWidth = useStore(useComponentDataListStoreContext, (state) => state.bucketWidth)
  const usePaperDataStoreContext = useContext(PaperDataStoreContext)
  const initPaperContent = useStore(usePaperDataStoreContext, (state) => state.initPaperContent)
  const questionNoRef = useRef<string | undefined>(undefined)
  const answerAreaKeyRef = useRef<AnswerAreaValueKeyEnum>(AnswerAreaValueKeyEnum.Choices)
  const addQuestion = useCallback(() => {
    if (!nowAddingQuestionInfo) {
      return
    }
    //往题组内新增
    nowAddingQuestionInfo.questionGroup.questions.push(
      (() => {
        const items: QuestionUnitVO[] = []
        const questionId = v4()
        const paperQuestion: PaperQuestionVO = {
          questionId,
          answers: [],
          question: {
            id: questionId,
            questionTypeCode: '',
            questionNo: questionNoRef.current,
            items,
          },
          questionNumber: '',
        }
        switch (answerAreaKeyRef.current) {
          case AnswerAreaValueKeyEnum.Choices:
            {
              items.push({
                key: QuestionUnitKeyEnum.Content,
                value: { content: '### 新增选择题 ###' } satisfies ContentDataVO,
              })
              items.push({
                key: QuestionUnitKeyEnum.Choice,
                value: {
                  choices: ['新增选项A', '新增选项B', '新增选项C', '新增选项D'],
                  answerArea: {
                    id: 0,
                    questionId,
                    value: {
                      key: AnswerAreaValueKeyEnum.Choices,
                    },
                  },
                } satisfies ChoiceDataVO,
              })
            }
            break
          case AnswerAreaValueKeyEnum.Inline:
            {
              items.push({
                key: QuestionUnitKeyEnum.Content,
                value: {
                  content: '### 新增填空题 ###',
                  answerArea: {
                    id: 0,
                    questionId,
                    value: {
                      key: AnswerAreaValueKeyEnum.Inline,
                      value: 4,
                    },
                  },
                } satisfies ContentDataVO,
              })
            }
            break
          case AnswerAreaValueKeyEnum.Multiline:
            {
              items.push({
                key: QuestionUnitKeyEnum.Content,
                value: {
                  content: '### 新增多行填空题 ###',
                  answerArea: {
                    id: 0,
                    questionId,
                    value: {
                      key: AnswerAreaValueKeyEnum.Multiline,
                      value: 4,
                    },
                  },
                } satisfies ContentDataVO,
              })
            }
            break
          case AnswerAreaValueKeyEnum.Composition:
            {
              items.push({
                key: QuestionUnitKeyEnum.Content,
                value: {
                  content: '### 新增作文题 ###',
                  answerArea: {
                    id: 0,
                    questionId,
                    value: {
                      key: AnswerAreaValueKeyEnum.Composition,
                      value: 800,
                    },
                  },
                } satisfies ContentDataVO,
              })
            }
            break
          case AnswerAreaValueKeyEnum.Wrap:
          case AnswerAreaValueKeyEnum.TrueOrFalse:
            {
              items.push({
                key: QuestionUnitKeyEnum.Content,
                value: {
                  content: '### 新增判断题 ###',
                  answerArea: {
                    id: 0,
                    questionId,
                    value: {
                      key: AnswerAreaValueKeyEnum.TrueOrFalse,
                    },
                  },
                } satisfies ContentDataVO,
              })
            }
            break
          case AnswerAreaValueKeyEnum.ImageLocation:
            {
              items.push({
                key: QuestionUnitKeyEnum.Illustration,
                value: {
                  url: '',
                  width: 200,
                  height: 200,
                  answerAreas: [
                    {
                      id: 0,
                      questionId,
                      value: {
                        key: AnswerAreaValueKeyEnum.ImageLocation,
                        value: [0, 0, 100, 50],
                      },
                    },
                  ],
                } satisfies IllustrationDataVO,
              })
            }
            break
        }
        return paperQuestion
      })()
    )
    //重新渲染
    initPaperContent(nowAddingQuestionInfo.paperContent, nowAddingQuestionInfo.paperContentIndex, bucketWidth)
    setNowAddingQuestionInfo()
  }, [bucketWidth, initPaperContent, nowAddingQuestionInfo, setNowAddingQuestionInfo])
  return (
    <QuestionCardUI.Dialog
      open={nowAddingQuestionInfo !== undefined}
      title={`新增题目`}
      onClose={() => {
        setNowAddingQuestionInfo()
        questionNoRef.current = undefined
        answerAreaKeyRef.current = AnswerAreaValueKeyEnum.Choices
      }}
      footer={
        <QuestionCardUI.Flex gap={10}>
          <QuestionCardUI.Button onClick={() => setNowAddingQuestionInfo()}>取 消</QuestionCardUI.Button>
          <QuestionCardUI.Button buttonType='primary' onClick={addQuestion}>
            新 增
          </QuestionCardUI.Button>
        </QuestionCardUI.Flex>
      }
      style={{ width: '400px' }}
    >
      <QuestionCardUI.Flex vertical gap={10} className={styles['form-item']}>
        <QuestionCardUI.Flex gap={10} className={styles['form-item']}>
          <div className={styles['title']}>题号</div>
          <QuestionCardUI.Input className={styles['flex-grow-1']} onChange={(v) => (questionNoRef.current = v.target.value)}></QuestionCardUI.Input>
        </QuestionCardUI.Flex>
        <QuestionCardUI.Flex gap={10} className={styles['form-item']}>
          <div className={styles['title']}>题目类型</div>
          <QuestionCardUI.Select
            className={styles['flex-grow-1']}
            options={getAnswerAreaValueKeyEnumOptions()}
            defaultValue={answerAreaKeyRef.current}
            onValueChange={(v) => (answerAreaKeyRef.current = v as AnswerAreaValueKeyEnum)}
          ></QuestionCardUI.Select>
        </QuestionCardUI.Flex>
      </QuestionCardUI.Flex>
    </QuestionCardUI.Dialog>
  )
}
