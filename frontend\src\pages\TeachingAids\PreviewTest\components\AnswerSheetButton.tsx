import { createDefaultAnswerCard } from '@/components/question-card/v1.0.0/store/paperDataStore'
import { Button } from '@/components/ui/button'
import { getTenantInfoFromLocalStorage } from '@/lib/apiUtils'
import { answerSheetApi } from '@/services/answerSheetApi'
import { sectionAnswerSheetApi } from '@/services/sectionAnswerSheetApi'
import { SectionDetail } from '@/types/teachingAid'
import { useCallback, useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'

export interface Props {
  section: SectionDetail
}

/**
 * 作者：张瀚
 * 说明：教辅中用于查看或者创建答题卡的按钮
 */
export function AnswerSheetButton({ section }: Props) {
  const { id: sectionId } = section
  const nowTenantInfo = getTenantInfoFromLocalStorage()
  const [tenantInfo, setTenantInfo] = useState(nowTenantInfo)
  if (!tenantInfo || JSON.stringify(tenantInfo) !== JSON.stringify(nowTenantInfo)) {
    setTenantInfo(nowTenantInfo)
  }
  const [hasAnswerSheet, setHasAnswerSheet] = useState<boolean | undefined>(undefined)
  const [answerSheetIds, setAnswerSheetIds] = useState<String[]>([])
  const navigate = useNavigate()

  const loadAnswerSheet = useCallback(() => {
    //查询关联的答题卡
    if (!tenantInfo) {
      return
    }
    sectionAnswerSheetApi.findBySectionId(tenantInfo.tenant_id, { sectionId }).then((res) => {
      const { success, data, message } = res
      if (!success) {
        console.error('报错:' + message)
        return
      }
      setHasAnswerSheet((data?.length ?? 0) > 0)
      setAnswerSheetIds(data ?? [])
    })
  }, [sectionId, tenantInfo])
  //创建答题卡
  const createAnswerSheet = useCallback(() => {
    if (!tenantInfo) {
      return
    }
    const { dataVersion, renderData } = createDefaultAnswerCard()
    answerSheetApi
      .createAnswerSheet(tenantInfo.tenant_id, {
        sectionId,
        dataVersion,
        renderData,
      })
      .then((res) => {
        const { success, data, message } = res
        if (!success) {
          console.error('报错:' + message)
          return
        }
        loadAnswerSheet()
      })
  }, [loadAnswerSheet, sectionId, tenantInfo])
  useEffect(() => {
    loadAnswerSheet()
  }, [loadAnswerSheet, sectionId, tenantInfo])
  //跳转到答题卡页面
  const toAnswerSheet = useCallback(() => {
    //目前先只支持一张答题卡
    if (answerSheetIds.length > 0) {
      navigate(`/answerSheetEditing/public_answer_sheet/${answerSheetIds[0]}`)
    }
  }, [answerSheetIds, navigate])
  return (
    <>
      {hasAnswerSheet !== undefined &&
        (hasAnswerSheet ? (
          <Button size='sm' onClick={toAnswerSheet}>
            查看答题卡
          </Button>
        ) : (
          <Button size='sm' onClick={createAnswerSheet}>
            创建答题卡
          </Button>
        ))}
    </>
  )
}
