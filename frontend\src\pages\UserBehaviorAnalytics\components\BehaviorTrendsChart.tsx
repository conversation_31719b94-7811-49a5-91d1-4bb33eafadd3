import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { LineChart, BarChart3 } from 'lucide-react';
import type { UserBehaviorTrend } from '@/services/analyticsApi';

interface BehaviorTrendsChartProps {
  behaviorTrends: UserBehaviorTrend[];
  timeRange: string;
  getTimeRangeLabel: (range: string) => string;
}

export const BehaviorTrendsChart: React.FC<BehaviorTrendsChartProps> = ({
  behaviorTrends,
  timeRange,
  getTimeRangeLabel
}) => {
  const maxUsers = behaviorTrends.length > 0 ? Math.max(...behaviorTrends.map(t => t.unique_users)) : 1;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <LineChart className="h-5 w-5 mr-2" />
          用户活跃度趋势 ({getTimeRangeLabel(timeRange)})
        </CardTitle>
        <CardDescription>
          展示用户活跃度、页面浏览量和操作次数的变化趋势
        </CardDescription>
      </CardHeader>
      <CardContent>
        {behaviorTrends.length > 0 ? (
          <div className="h-64 flex items-end space-x-2 p-4 bg-gradient-to-b from-blue-50/50 to-transparent rounded-lg">
            {behaviorTrends.map((trend) => {
              const height = (trend.unique_users / maxUsers) * 100;
              return (
                <div key={trend.date} className="flex-1 flex flex-col items-center group">
                  <div
                    className="bg-gradient-to-t from-blue-600 to-blue-400 w-full rounded-t transition-all duration-300 hover:from-blue-700 hover:to-blue-500 cursor-pointer relative"
                    style={{ height: `${height}%`, minHeight: '8px' }}
                    title={`${trend.date}: ${trend.unique_users} 活跃用户`}
                  >
                    {/* 悬浮显示详细信息 */}
                    <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white px-2 py-1 rounded text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10">
                      {trend.unique_users} 用户
                    </div>
                  </div>
                  <div className="text-xs mt-2 text-muted-foreground font-medium">
                    {new Date(trend.date).toLocaleDateString('zh-CN', {
                      month: 'short',
                      day: 'numeric'
                    })}
                  </div>
                  <div className="text-xs text-blue-600 font-semibold mt-1">
                    {trend.unique_users}
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <div className="h-64 flex items-center justify-center text-muted-foreground">
            <div className="text-center">
              <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium">暂无趋势数据</p>
              <p className="text-sm">数据收集中，请稍后查看</p>
            </div>
          </div>
        )}

        {/* 图例 */}
        <div className="flex items-center justify-center mt-4 space-x-6 text-sm">
          <div className="flex items-center">
            <div className="w-3 h-3 bg-gradient-to-r from-blue-600 to-blue-400 rounded mr-2"></div>
            <span>唯一用户数</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}; 