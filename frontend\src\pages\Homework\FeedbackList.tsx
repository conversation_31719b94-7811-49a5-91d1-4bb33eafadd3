import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Search, Eye, X } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog'
import CustomPagination from '@/components/Pagination'
import { HomeworkStatusEnum } from '@/types/homework'
import { Table, TableBody, TableCaption, TableCell, TableFooter, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { HomeworkFeedbackStatus } from '@/services/studentScoreApi'
import { useHomeworkStore } from '@/stores'
import { useNavigate } from 'react-router-dom'
import { studentScoreApi, TeachingClassesResponse, FeedbackListResponse } from '@/services/studentScoreApi'
import { getTenantInfoFromLocalStorage } from '@/lib/apiUtils'
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet'
import ScanImagePreview from './ScanImagePreview'
import { type Student } from '@/services/scanApi'
import { toast } from 'sonner'
import { Scores, homeworkReviewApi } from '@/services/homeworkReviewApi'
import DetailsDrawer from './components/DetailsDrawer'

const FeedbackList: React.FC = () => {
  const [error, setError] = useState<string | null>(null)
  const [searchParams, setSearchParams] = useState({
    name: '',
  })
  const navigate = useNavigate()
  const { homework } = useHomeworkStore()
  const [FeedbackList, setFeedbackList] = useState<FeedbackListResponse[]>([])
  const [statusFilter, setStatusFilter] = useState<HomeworkFeedbackStatus | 'all'>('all')
  const [page, setPage] = useState(1)
  const [page_size, setPageSize] = useState(20)
  const [total, setTotal] = useState(10)
  const [teachingClasses, setTeachingClasses] = useState<TeachingClassesResponse[]>([])
  const identityInfo = getTenantInfoFromLocalStorage()
  const schema_name = identityInfo?.schema_name || ''
  const [detailsOpen, setDetailsOpen] = useState(false)
  const [editingScore, setEditingScore] = useState<{
    id: string
    score: number
  }>()
  const [rejectConfirmOpen, setRejectConfirmOpen] = useState(false)
  const [selectedFeedbackId, setSelectedFeedbackId] = useState<string | null>(null)
  const [popoverOpen, setPopoverOpen] = useState<string | null>(null)

  useEffect(() => {
    if (!schema_name) return
    getTeachingClasses()
  }, [schema_name])

  useEffect(() => {
    if (teachingClasses.length === 0) return
    getPageList()
  }, [teachingClasses, statusFilter, page, page_size])

  const getTeachingClasses = async () => {
    const res = await studentScoreApi.getTeachingClasses(schema_name)
    if (res.success) {
      setTeachingClasses(res.data ?? [])
      console.log(teachingClasses, 'teachingClasses')
    }
  }

  const getPageList = async () => {
    const res = await studentScoreApi.getPageList(schema_name, {
      homework_ids: [],
      teaching_class_ids: teachingClasses.map((item) => item.id),
      page_params: {
        page,
        page_size,
      },
      status_list: statusFilter === 'all' ? [] : [statusFilter],
    })
    if (res.success) {
      setFeedbackList(res.data ?? [])
      setTotal(res.pagination?.total ?? 0)
    }
  }

  const paginationChange = async (page: number, pageSize: number) => {
    await new Promise((resolve) => {
      setPage(page)
      setPageSize(pageSize)
      setTimeout(resolve, 0)
    })
    getPageList()
  }

  const getStudentInfo = (student: Student | null) => {
    const { student_number, student_id, student_name } = student || {}

    const parts = []

    if (student_number !== null) {
      parts.push(student_number)
    }
    if (student_name !== null) {
      parts.push(student_name)
    }
    if (!student_id) {
      parts.push('未绑定')
    }
    const resultText = parts.join(' ')
    const resultColor = student_id && student_id !== null ? 'text-green-700' : 'text-red-500'

    return {
      text: resultText,
      color: resultColor,
    }
  }

  // 保存分数
  const saveScore = (score?: number) => {
    if (editingScore?.score || editingScore?.score === 0) {
      homeworkReviewApi
        .updateQuestionScores(schema_name, {
          id: editingScore.id,
          score: score !== undefined ? score : editingScore.score,
          // reason: undefined,
        })
        .then((res) => {
          if (res.success) {
            toast.success('保存成功')
            getPageList()
          }
          setEditingScore({} as { id: string; score: number })
        })
    } else {
      alert('请输入分数')
    }
  }

  // 编辑分数
  const handleEditScore = (stu: Scores) => {
    setEditingScore({ id: stu.id, score: stu.score })
  }

  // 获取状态标签配置
  const getStatusBadgeConfig = (status: HomeworkFeedbackStatus) => {
    const configs = {
      Initial: {
        variant: 'default' as const,
        label: '已提交',
        className: 'bg-blue-100 text-blue-800 hover:bg-blue-200 border-blue-200',
      },
      Received: {
        variant: 'default' as const,
        label: '已处理',
        className: 'bg-green-100 text-green-800 hover:bg-green-200 border-green-200',
      },
      Rejected: {
        variant: 'destructive' as const,
        label: '已拒绝',
        className: 'bg-red-100 text-red-800 hover:bg-red-200 border-red-200',
      },
      Cancelled: {
        variant: 'secondary' as const,
        label: '已关闭',
        className: 'bg-gray-100 text-gray-800 hover:bg-gray-200 border-gray-200',
      },
      Resubmitted: {
        variant: 'outline' as const,
        label: '重新打开',
        className: 'bg-orange-100 text-orange-800 hover:bg-orange-200 border-orange-200',
      },
    }
    return configs[status] || configs.Initial
  }

  // 处理拒绝反馈
  const handleRejectFeedback = async () => {
    if (!selectedFeedbackId) return

    try {
      const selectedFeedback = FeedbackList.find((item) => item.id === selectedFeedbackId)
      if (!selectedFeedback) return

      const res = await studentScoreApi.updateFeedback(schema_name, {
        id: selectedFeedbackId,
        status: 'Rejected',
        text: selectedFeedback.text, // 保持原有文本不变
      })

      if (res.success) {
        toast.success('反馈状态已更新为已拒绝')
        getPageList() // 刷新列表
      } else {
        toast.error('更新失败，请重试')
      }
    } catch (error) {
      console.error('更新反馈状态失败:', error)
      toast.error('更新失败，请重试')
    } finally {
      setRejectConfirmOpen(false)
      setSelectedFeedbackId(null)
    }
  }

  // 处理点击拒绝按钮
  const handleRejectClick = (feedbackId: string) => {
    setSelectedFeedbackId(feedbackId)
    setRejectConfirmOpen(true)
  }

  return (
    <div>
      <Card className='flex flex-col flex-grow'>
        <CardHeader className='flex flex-row items-center justify-between'>
          {error && <div className='mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded'>{error}</div>}

          <h2 className='text-2xl font-bold'>学生反馈</h2>
          <div className='flex gap-4 items-center'>
            {/* <div className='relative flex-1 w-[250px]'>
              <Search className='absolute left-3 top-3 h-4 w-4 text-muted-foreground' />
              <Input placeholder='请输入反馈关键字进行搜索' value={searchParams.name} onChange={(e) => setSearchParams({ ...searchParams, name: e.target.value })} className='pl-10' />
            </div> */}
            <Select value={statusFilter} onValueChange={(value) => setStatusFilter(value as HomeworkFeedbackStatus | 'all')}>
              <SelectTrigger className='w-[160px]'>
                <SelectValue placeholder='选择状态' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='all'>全部状态</SelectItem>
                {Object.entries({
                  Initial: '已提交',
                  Received: '已处理',
                  Rejected: '已拒绝',
                  Cancelled: '已关闭',
                  Resubmitted: '重新打开',
                }).map(([value, label]) => (
                  <SelectItem key={value} value={value}>
                    {label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button>
              <Search className='h-4 w-4 mr-2' />
              查询
            </Button>
          </div>
        </CardHeader>
        <CardContent className='flex-grow min-h-[200px]'>
          <div>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className='w-[120px]'>学生姓名</TableHead>
                  <TableHead className='w-[120px]'>学号</TableHead>
                  <TableHead>反馈内容</TableHead>
                  <TableHead>原图</TableHead>
                  <TableHead className='w-[180px]'>状态</TableHead>
                  <TableHead className='w-[120px]'>操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {FeedbackList.map((item, Index) => (
                  <TableRow key={Index}>
                    <TableCell className='font-medium'>{item.score?.student?.student_name}</TableCell>
                    <TableCell>{item.score?.student?.student_number}</TableCell>
                    <TableCell>{item.text}</TableCell>
                    <TableCell>
                      {item.score?.blocks?.length > 0 && (
                        <Card className='relative'>
                          {item.score?.blocks.map((image: any, imgIndex: number) => (
                            <div key={image.id}>
                              <div className='relative w-full h-auto border rounded'>
                                <img src={image.answer_block_url as string} alt={`学生${image.student_id}答题图片`} className='object-scale-down' />
                                <div className={`absolute bottom-2 left-2 z-39 text-sm ${getStudentInfo(item.score?.student).color}`}>{getStudentInfo(item.score?.student).text}</div>
                                {imgIndex === 0 && (
                                  <div className='absolute top-2 left-2 z-39'>
                                    {editingScore?.id === item.score?.id ? (
                                      <div className='bg-white/95 backdrop-blur-sm rounded p-2 shadow-xl border z-50'>
                                        <Input
                                          type='number'
                                          value={editingScore?.score}
                                          min={0}
                                          onChange={(e) =>
                                            setEditingScore((prev) =>
                                              prev
                                                ? {
                                                    ...prev,
                                                    score: Number(e.target.value),
                                                  }
                                                : undefined
                                            )
                                          }
                                          className='w-20 h-8 text-center'
                                        />
                                        <div className='gap-1 mt-2'>
                                          <div className='mb-2 gap-1 flex'>
                                            <Button
                                              size='sm'
                                              onClick={() => {
                                                saveScore()
                                              }}
                                            >
                                              保存
                                            </Button>
                                            <Button size='sm' variant='outline' onClick={() => setEditingScore({} as { id: string; score: number })}>
                                              取消
                                            </Button>
                                          </div>
                                        </div>
                                      </div>
                                    ) : (
                                      <div
                                        className='text-red-600 font-bold text-lg cursor-pointer hover:text-red-700 drop-shadow-lg'
                                        onClick={() => handleEditScore(item.score)}
                                        style={{
                                          textShadow: '1px 1px 2px rgba(255,255,255,0.8)',
                                        }}
                                      >
                                        {item.score.score}分
                                      </div>
                                    )}
                                  </div>
                                )}
                                {imgIndex === 0 && (
                                  <div className='absolute top-2 right-2'>
                                    <DetailsDrawer
                                      details={item.score.details || []}
                                      blocks={item.score.blocks}
                                      title='评分详情'
                                      trigger={
                                        <Button size='sm' variant='ghost' className='h-8 w-8 p-0 text-gray-600 hover:text-gray-800'>
                                          <Eye
                                            className={`h-4 w-4 ${item.score.status === 'CheckedCorrect' ? 'text-green-700' : item.score.status === 'CheckedError' ? 'text-red-500' : 'text-gray-500'}`}
                                          />
                                        </Button>
                                      }
                                    />
                                  </div>
                                )}
                              </div>
                            </div>
                          ))}
                        </Card>
                      )}
                    </TableCell>
                    <TableCell>
                      {item.status === 'Initial' ? (
                        <Popover open={popoverOpen === item.id} onOpenChange={(open) => setPopoverOpen(open ? item.id : null)}>
                          <PopoverTrigger asChild>
                            <Badge
                              className={`cursor-pointer ${getStatusBadgeConfig(item.status).className}`}
                              variant={getStatusBadgeConfig(item.status).variant}
                              onMouseEnter={() => setPopoverOpen(item.id)}
                              onMouseLeave={() => setPopoverOpen(null)}
                            >
                              {getStatusBadgeConfig(item.status).label}
                            </Badge>
                          </PopoverTrigger>
                          <PopoverContent className='w-auto p-2' side='top' align='center'>
                            <Button
                              size='sm'
                              variant='destructive'
                              onClick={() => {
                                handleRejectClick(item.id)
                                setPopoverOpen(null)
                              }}
                              className='h-8 px-3 text-xs'
                            >
                              <X className='h-3 w-3 mr-1' />
                              拒绝反馈
                            </Button>
                          </PopoverContent>
                        </Popover>
                      ) : (
                        <Badge className={getStatusBadgeConfig(item.status).className} variant={getStatusBadgeConfig(item.status).variant}>
                          {getStatusBadgeConfig(item.status).label}
                        </Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      {
                        <Sheet open={detailsOpen} onOpenChange={setDetailsOpen}>
                          <SheetTrigger asChild>
                            <Button variant='link' className='p-0 text-blue-500'>
                              {item.homework_name}
                            </Button>
                          </SheetTrigger>
                          <SheetContent side='right' className='h-full overflow-y-auto w-[80vw] sm:max-w-[80vw]'>
                            <SheetHeader>
                              <SheetTitle>处理学生反馈</SheetTitle>
                            </SheetHeader>
                            <div className='mt-4'>
                              <ScanImagePreview paperId={item.student_id} homeworkId={item.homework_id} />
                            </div>
                          </SheetContent>
                        </Sheet>
                      }
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
          <div className='flex justify-center'>
            <CustomPagination
              className='pt-2'
              total={total}
              current={page}
              pageSize={page_size}
              onChange={(page, pageSize) => {
                paginationChange(page, pageSize)
              }}
              showSizeChanger={true}
              showTotal={true}
            />
          </div>
        </CardContent>
      </Card>

      {/* 拒绝反馈确认对话框 */}
      <AlertDialog open={rejectConfirmOpen} onOpenChange={setRejectConfirmOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认拒绝反馈</AlertDialogTitle>
            <AlertDialogDescription>您确定要拒绝这条学生反馈吗？此操作将把反馈状态更改为&ldquo;已拒绝&rdquo;。</AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setRejectConfirmOpen(false)}>取消</AlertDialogCancel>
            <AlertDialogAction onClick={handleRejectFeedback} className='bg-red-600 hover:bg-red-700'>
              确认拒绝
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}

export default FeedbackList
