import React, { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Search, Eye } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Button } from '@/components/ui/button'
import CustomPagination from '@/components/Pagination'
import { HomeworkStatusEnum } from '@/types/homework'
import { Table, TableBody, TableCaption, TableCell, TableFooter, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { HomeworkFeedbackStatus } from '@/services/studentScoreApi'
import { useHomeworkStore } from '@/stores'
import { useNavigate } from 'react-router-dom'
import { studentScoreApi, TeachingClassesResponse, FeedbackListResponse } from '@/services/studentScoreApi'
import { getTenantInfoFromLocalStorage } from '@/lib/apiUtils'
import { Sheet, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ead<PERSON>, She<PERSON><PERSON><PERSON><PERSON>, She<PERSON><PERSON>rigger } from '@/components/ui/sheet'
import ScanImagePreview from './ScanImagePreview'
import { type Student } from '@/services/scanApi'
import { toast } from 'sonner'
import { Scores, homeworkReviewApi } from '@/services/homeworkReviewApi'
import DetailsDrawer from './components/DetailsDrawer'

const FeedbackList: React.FC = () => {
  const [error, setError] = useState<string | null>(null)
  const [searchParams, setSearchParams] = useState({
    name: '',
  })
  const navigate = useNavigate()
  const { homework } = useHomeworkStore()
  const [FeedbackList, setFeedbackList] = useState<FeedbackListResponse[]>([])
  const [statusFilter, setStatusFilter] = useState<HomeworkFeedbackStatus | 'all'>('all')
  const [page, setPage] = useState(1)
  const [page_size, setPageSize] = useState(20)
  const [total, setTotal] = useState(10)
  const [teachingClasses, setTeachingClasses] = useState<TeachingClassesResponse[]>([])
  const identityInfo = getTenantInfoFromLocalStorage()
  const schema_name = identityInfo?.schema_name || ''
  const [detailsOpen, setDetailsOpen] = useState(false)
  const [editingScore, setEditingScore] = useState<{
    id: string
    score: number
  }>()

  useEffect(() => {
    if (!schema_name) return
    getTeachingClasses()
  }, [schema_name])

  useEffect(() => {
    if (teachingClasses.length === 0) return
    getPageList()
  }, [teachingClasses])

  const getTeachingClasses = async () => {
    const res = await studentScoreApi.getTeachingClasses(schema_name)
    if (res.success) {
      setTeachingClasses(res.data ?? [])
      console.log(teachingClasses, 'teachingClasses')
    }
  }

  const getPageList = async () => {
    const res = await studentScoreApi.getPageList(schema_name, {
      homework_ids: [],
      teaching_class_ids: teachingClasses.map((item) => item.id),
      page_params: {
        page,
        page_size,
      },
      status_list: statusFilter === 'all' ? [] : [statusFilter],
    })
    if (res.success) {
      setFeedbackList(res.data ?? [])
      setTotal(res.pagination?.total ?? 0)
    }
  }

  const paginationChange = async (page: number, pageSize: number) => {
    await new Promise((resolve) => {
      setPage(page)
      setPageSize(pageSize)
      setTimeout(resolve, 0)
    })
    getPageList()
  }

  const getStudentInfo = (student: Student | null) => {
    const { student_number, student_id, student_name } = student || {}

    const parts = []

    if (student_number !== null) {
      parts.push(student_number)
    }
    if (student_name !== null) {
      parts.push(student_name)
    }
    if (!student_id) {
      parts.push('未绑定')
    }
    const resultText = parts.join(' ')
    const resultColor = student_id && student_id !== null ? 'text-green-700' : 'text-red-500'

    return {
      text: resultText,
      color: resultColor,
    }
  }

  // 保存分数
  const saveScore = (score?: number) => {
    if (editingScore?.score || editingScore?.score === 0) {
      homeworkReviewApi
        .updateQuestionScores(schema_name, {
          id: editingScore.id,
          score: score !== undefined ? score : editingScore.score,
          // reason: undefined,
        })
        .then((res) => {
          if (res.success) {
            toast.success('保存成功')
            getPageList()
          }
          setEditingScore({} as { id: string; score: number })
        })
    } else {
      alert('请输入分数')
    }
  }

  // 编辑分数
  const handleEditScore = (stu: Scores) => {
    setEditingScore({ id: stu.id, score: stu.score })
  }

  return (
    <div>
      <Card className='flex flex-col flex-grow'>
        <CardHeader className='flex flex-row items-center justify-between'>
          {error && <div className='mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded'>{error}</div>}

          <h2 className='text-2xl font-bold'>学生反馈</h2>
          <div className='flex gap-4 items-center'>
            {/* <div className='relative flex-1 w-[250px]'>
              <Search className='absolute left-3 top-3 h-4 w-4 text-muted-foreground' />
              <Input placeholder='请输入反馈关键字进行搜索' value={searchParams.name} onChange={(e) => setSearchParams({ ...searchParams, name: e.target.value })} className='pl-10' />
            </div> */}
            <Select value={statusFilter} onValueChange={(value) => setStatusFilter(value as HomeworkFeedbackStatus | 'all')}>
              <SelectTrigger className='w-[160px]'>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='all'>全部状态</SelectItem>
                <SelectItem value='Initial'>已提交</SelectItem>
                <SelectItem value='Received'>已处理</SelectItem>
                <SelectItem value='Rejected'>已拒绝</SelectItem>
                <SelectItem value='Cancelled'>已关闭</SelectItem>
                <SelectItem value='Resubmitted'>重新打开</SelectItem>
              </SelectContent>
            </Select>
            <Button>
              <Search className='h-4 w-4 mr-2' />
              查询
            </Button>
          </div>
        </CardHeader>
        <CardContent className='flex-grow min-h-[200px]'>
          <div>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className='w-[120px]'>学生姓名</TableHead>
                  <TableHead className='w-[120px]'>学号</TableHead>
                  <TableHead>反馈内容</TableHead>
                  <TableHead>原图</TableHead>
                  <TableHead className='w-[180px]'>状态</TableHead>
                  <TableHead className='w-[120px]'>操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {FeedbackList.map((item, Index) => (
                  <TableRow key={Index}>
                    <TableCell className='font-medium'>{item.score?.student?.student_name}</TableCell>
                    <TableCell>{item.score?.student?.student_number}</TableCell>
                    <TableCell>{item.text}</TableCell>
                    <TableCell>
                      {item.score?.blocks?.length > 0 && (
                        <Card className='relative'>
                          {item.score?.blocks.map((image: any, imgIndex: number) => (
                            <div key={image.id}>
                              <div className='relative w-full h-auto border rounded'>
                                <img src={image.answer_block_url as string} alt={`学生${image.student_id}答题图片`} className='object-scale-down' />
                                <div className={`absolute bottom-2 left-2 z-39 text-sm ${getStudentInfo(item.score?.student).color}`}>{getStudentInfo(item.score?.student).text}</div>
                                {imgIndex === 0 && (
                                  <div className='absolute top-2 left-2 z-39'>
                                    {editingScore?.id === item.score?.id ? (
                                      <div className='bg-white/95 backdrop-blur-sm rounded p-2 shadow-xl border z-50'>
                                        <Input
                                          type='number'
                                          value={editingScore?.score}
                                          min={0}
                                          onChange={(e) =>
                                            setEditingScore((prev) =>
                                              prev
                                                ? {
                                                    ...prev,
                                                    score: Number(e.target.value),
                                                  }
                                                : undefined
                                            )
                                          }
                                          className='w-20 h-8 text-center'
                                        />
                                        <div className='gap-1 mt-2'>
                                          <div className='mb-2 gap-1 flex'>
                                            <Button
                                              size='sm'
                                              onClick={() => {
                                                saveScore()
                                              }}
                                            >
                                              保存
                                            </Button>
                                            <Button size='sm' variant='outline' onClick={() => setEditingScore({} as { id: string; score: number })}>
                                              取消
                                            </Button>
                                          </div>
                                        </div>
                                      </div>
                                    ) : (
                                      <div
                                        className='text-red-600 font-bold text-lg cursor-pointer hover:text-red-700 drop-shadow-lg'
                                        onClick={() => handleEditScore(item.score)}
                                        style={{
                                          textShadow: '1px 1px 2px rgba(255,255,255,0.8)',
                                        }}
                                      >
                                        {item.score.score}分
                                      </div>
                                    )}
                                  </div>
                                )}
                                {imgIndex === 0 && (
                                  <div className='absolute top-2 right-2'>
                                    <DetailsDrawer
                                      details={item.score.details || []}
                                      blocks={item.score.blocks}
                                      title='评分详情'
                                      trigger={
                                        <Button size='sm' variant='ghost' className='h-8 w-8 p-0 text-gray-600 hover:text-gray-800'>
                                          <Eye
                                            className={`h-4 w-4 ${item.score.status === 'CheckedCorrect' ? 'text-green-700' : item.score.status === 'CheckedError' ? 'text-red-500' : 'text-gray-500'}`}
                                          />
                                        </Button>
                                      }
                                    />
                                  </div>
                                )}
                              </div>
                            </div>
                          ))}
                        </Card>
                      )}
                    </TableCell>
                    <TableCell>{item.status}</TableCell>
                    <TableCell>
                      {
                        <Sheet open={detailsOpen} onOpenChange={setDetailsOpen}>
                          <SheetTrigger asChild>
                            <Button variant='link' className='p-0 text-blue-500'>
                              {item.homework_name}
                            </Button>
                          </SheetTrigger>
                          <SheetContent side='right' className='h-full overflow-y-auto w-[80vw] sm:max-w-[80vw]'>
                            <SheetHeader>
                              <SheetTitle>处理学生反馈</SheetTitle>
                            </SheetHeader>
                            <div className='mt-4'>
                              <ScanImagePreview paperId={item.student_id} homeworkId={item.homework_id} />
                            </div>
                          </SheetContent>
                        </Sheet>
                      }
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
          <div className='flex justify-center'>
            <CustomPagination
              className='pt-2'
              total={total}
              current={page}
              pageSize={page_size}
              onChange={(page, pageSize) => {
                paginationChange(page, pageSize)
              }}
              showSizeChanger={true}
              showTotal={true}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default FeedbackList
