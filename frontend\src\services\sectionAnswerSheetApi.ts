import { createApiHeaders } from '@/lib/apiUtils'
import { ApiResponse } from '@/types'
import apiClient from './apiClient'

const API_BASE_URL = '/api/v1'

/**
 * 作者：张瀚
 * 说明：公共域的内容块和答题卡关联接口
 */
export const sectionAnswerSheetApi = {
  /**
   * 作者：张瀚
   * 说明：查询内容块关联的答题卡ID列表
   */
  findBySectionId: async (tenant_id: string, params: FindBySectionIdParams): Promise<ApiResponse<String[]>> => {
    return apiClient.post(`${API_BASE_URL}/tenants/public/sectionAnswerSheet/findBySectionId`, params, {
      headers: createApiHeaders(tenant_id),
    })
  },
}

export interface FindBySectionIdParams {
  sectionId: string
}
