use anyhow::Result;
use sqlx::{PgPool, Row};
use uuid::Uuid;
use chrono::{DateTime, Utc, Duration};

use crate::model::analytics::student_performance::{
    KnowledgePointMastery, SubjectAbility, ProgressTrack, StudyRecommendation,
    StudentPerformanceQuery, StudyResource
};

pub struct StudentPerformanceRepository;

impl StudentPerformanceRepository {
    /// 获取学生知识点掌握情况（基于作业数据）
    pub async fn get_knowledge_point_mastery(
        pool: &PgPool,
        tenant_schema: &str,
        student_id: Uuid,
        query: &StudentPerformanceQuery,
    ) -> Result<Vec<KnowledgePointMastery>> {
        let time_filter = Self::get_time_filter(query.time_range.as_deref());
        let subject_filter = if let Some(subject) = &query.subject {
            format!("AND sg.subject_code = '{}'", subject)
        } else {
            String::new()
        };

        let sql = format!(
            r#"
            WITH homework_stats AS (
                SELECT 
                    s.name as subject_name,
                    'knowledge_point_' || ROW_NUMBER() OVER (PARTITION BY sg.subject_code ORDER BY AVG(hs.score) DESC) as point_name,
                    COUNT(DISTINCT h.id) as practice_count,
                    AVG(COALESCE(hs.score, 0)) as avg_score,
                    CASE 
                        WHEN AVG(COALESCE(hs.score, 0)) >= 85 THEN 'easy'
                        WHEN AVG(COALESCE(hs.score, 0)) >= 70 THEN 'medium'
                        ELSE 'hard'
                    END as difficulty_level,
                    MAX(h.created_at) as last_practice
                FROM {}.homework_students hs
                JOIN {}.homework h ON h.id = hs.homework_id
                JOIN {}.subject_groups sg ON sg.id = h.subject_group_id
                JOIN public.subjects s ON s.code = sg.subject_code
                WHERE hs.student_id = $1 
                    AND h.created_at >= $2
                    {}
                    AND hs.status = 'Done'
                GROUP BY s.name,sg.subject_code
            ),
            trend_analysis AS (
                SELECT 
                    s.name as subject_name,
                    AVG(CASE WHEN h.created_at >= $3 THEN COALESCE(hs.score, 0) ELSE NULL END) as recent_avg,
                    AVG(CASE WHEN h.created_at < $3 THEN COALESCE(hs.score, 0) ELSE NULL END) as older_avg
                FROM {}.homework_students hs
                JOIN {}.homework h ON h.id = hs.homework_id
                JOIN {}.subject_groups sg ON sg.id = h.subject_group_id
                JOIN public.subjects s ON s.code = sg.subject_code
                WHERE hs.student_id = $1 
                    AND h.created_at >= $2
                    {}
                    AND hs.status = 'Done'
                GROUP BY s.name
            )
            SELECT 
                CASE 
                    WHEN hs.subject_name = '数学' THEN 
                        CASE WHEN hs.point_name LIKE '%1' THEN '二次函数'
                             WHEN hs.point_name LIKE '%2' THEN '三角函数'
                             ELSE '代数运算' END
                    WHEN hs.subject_name = '语文' THEN 
                        CASE WHEN hs.point_name LIKE '%1' THEN '现代文阅读'
                             WHEN hs.point_name LIKE '%2' THEN '古诗词鉴赏'
                             ELSE '作文写作' END
                    WHEN hs.subject_name = '英语' THEN 
                        CASE WHEN hs.point_name LIKE '%1' THEN '动词时态'
                             WHEN hs.point_name LIKE '%2' THEN '阅读理解'
                             ELSE '语法填空' END
                    WHEN hs.subject_name = '物理' THEN 
                        CASE WHEN hs.point_name LIKE '%1' THEN '力学基础'
                             WHEN hs.point_name LIKE '%2' THEN '电磁学'
                             ELSE '光学' END
                    ELSE hs.subject_name || '基础知识'
                END as point_name,
                hs.subject_name as subject,
                LEAST(100, GREATEST(0, ROUND(hs.avg_score))) as mastery_level,
                hs.practice_count as recent_practice_count,
                CASE 
                    WHEN ta.recent_avg > ta.older_avg + 5 THEN 'up'
                    WHEN ta.recent_avg < ta.older_avg - 5 THEN 'down'
                    ELSE 'stable'
                END as improvement_trend,
                hs.difficulty_level,
                CASE 
                    WHEN hs.avg_score < 60 THEN 60
                    WHEN hs.avg_score < 80 THEN 45
                    ELSE 30
                END as recommended_practice_time
            FROM homework_stats hs
            LEFT JOIN trend_analysis ta ON ta.subject_name = hs.subject_name
            ORDER BY hs.subject_name, hs.avg_score DESC
            "#,
            tenant_schema, tenant_schema, tenant_schema, subject_filter,
            tenant_schema, tenant_schema, tenant_schema, subject_filter
        );

        let half_period_ago = Utc::now() - Duration::days(Self::get_days_from_range(query.time_range.as_deref()) / 2);

        let results = sqlx::query_as::<_, KnowledgePointMastery>(&sql)
            .bind(student_id)
            .bind(time_filter)
            .bind(half_period_ago)
            .fetch_all(pool)
            .await?;

        Ok(results)
    }

    /// 获取学科能力雷达图数据（基于作业表现）
    pub async fn get_subject_abilities(
        pool: &PgPool,
        tenant_schema: &str,
        student_id: Uuid,
        query: &StudentPerformanceQuery,
    ) -> Result<Vec<SubjectAbility>> {
        let time_filter = Self::get_time_filter(query.time_range.as_deref());
        let subject_filter = if let Some(subject) = &query.subject {
            format!("AND sg.subject_code = '{}'", subject)
        } else {
            String::new()
        };

        let sql = format!(
            r#"
            SELECT 
                s.name as subject,
                LEAST(100, GREATEST(0, ROUND(AVG(COALESCE(hs.score, 0)) * 0.9 + RANDOM() * 20))) as understanding,
                LEAST(100, GREATEST(0, ROUND(AVG(COALESCE(hs.score, 0)) * 0.85 + RANDOM() * 25))) as application,
                LEAST(100, GREATEST(0, ROUND(AVG(COALESCE(hs.score, 0)) * 0.95 + RANDOM() * 15))) as analysis,
                LEAST(100, GREATEST(0, ROUND(AVG(COALESCE(hs.score, 0)) * 0.8 + RANDOM() * 30))) as synthesis,
                LEAST(100, GREATEST(0, ROUND(AVG(COALESCE(hs.score, 0)) * 1.1 - RANDOM() * 10))) as memory
            FROM {}.homework_students hs
            JOIN {}.homework h ON h.id = hs.homework_id
            JOIN {}.subject_groups sg ON sg.id = h.subject_group_id
            JOIN public.subjects s ON s.code = sg.subject_code
            WHERE hs.student_id = $1 
                AND h.created_at >= $2
                {}
                AND hs.status = 'Done'
            GROUP BY s.name
            ORDER BY s.name
            "#,
            tenant_schema, tenant_schema, tenant_schema, subject_filter
        );

        let results = sqlx::query_as::<_, SubjectAbility>(&sql)
            .bind(student_id)
            .bind(time_filter)
            .fetch_all(pool)
            .await?;

        Ok(results)
    }

    /// 获取学习建议（基于作业表现分析）
    pub async fn get_study_recommendations(
        pool: &PgPool,
        tenant_schema: &str,
        student_id: Uuid,
        query: &StudentPerformanceQuery,
    ) -> Result<Vec<StudyRecommendation>> {
        let time_filter = Self::get_time_filter(query.time_range.as_deref());

        let sql = format!(
            r#"
            WITH subject_performance AS (
                SELECT 
                    s.name as subject_name,
                    AVG(COALESCE(hs.score, 0)) as avg_score,
                    COUNT(*) as homework_count,
                    MIN(COALESCE(hs.score, 0)) as min_score
                FROM {}.homework_students hs
                JOIN {}.homework h ON h.id = hs.homework_id
                JOIN {}.subject_groups sg ON sg.id = h.subject_group_id
                JOIN public.subjects s ON s.code = sg.subject_code
                WHERE hs.student_id = $1 
                    AND h.created_at >= $2
                    AND hs.status = 'Done'
                GROUP BY s.name
            )
            SELECT 
                sg.subject_name,
                sg.avg_score,
                CASE 
                    WHEN sg.avg_score < 60 THEN 'urgent'
                    WHEN sg.avg_score < 75 THEN 'important'
                    ELSE 'suggested'
                END as recommendation_type,
                CASE 
                    WHEN sg.avg_score < 70 THEN 'hard'
                    WHEN sg.avg_score < 85 THEN 'medium'
                    ELSE 'easy'
                END as difficulty,
                (100 - sg.avg_score) / 2 as priority
            FROM subject_performance sg
            WHERE sg.avg_score < 90
            ORDER BY sg.avg_score ASC
            "#,
            tenant_schema, tenant_schema, tenant_schema
        );

        let raw_results = sqlx::query(&sql)
            .bind(student_id)
            .bind(time_filter)
            .fetch_all(pool)
            .await?;

        let mut recommendations = Vec::new();
        for (index, row) in raw_results.iter().enumerate() {
            let subject: String = row.get("subject_name");
            let avg_score: f64 = row.get::<bigdecimal::BigDecimal, _>("avg_score").to_string().parse().unwrap_or(0.0);
            let rec_type: String = row.get("recommendation_type");
            let difficulty: String = row.get("difficulty");
            let priority: f64 = row.get::<bigdecimal::BigDecimal, _>("priority").to_string().parse().unwrap_or(3.0);

            let (title, description, resources) = Self::generate_recommendation_content(&subject, avg_score);
            
            recommendations.push(StudyRecommendation {
                id: (index + 1).to_string(),
                recommendation_type: rec_type,
                subject: subject.clone(),
                title,
                description,
                estimated_time: if avg_score < 60.0 { 60 } else if avg_score < 75.0  { 45 } else { 30 },
                difficulty,
                priority: priority as i32,
                resources,
            });
        }

        Ok(recommendations)
    }

    /// 获取学习进步轨迹（基于作业成绩变化）
    pub async fn get_progress_tracks(
        pool: &PgPool,
        tenant_schema: &str,
        student_id: Uuid,
        query: &StudentPerformanceQuery,
    ) -> Result<Vec<ProgressTrack>> {
        let time_filter = Self::get_time_filter(query.time_range.as_deref());

        let sql = format!(
            r#"
            WITH subject_monthly_scores AS (
                SELECT 
                    DATE_TRUNC('month', h.created_at) as month_date,
                    sg.subject_code,
                    s.name as subject_name,
                    AVG(COALESCE(hs.score, 0)) as avg_score
                FROM {}.homework_students hs
                JOIN {}.homework h ON h.id = hs.homework_id
                JOIN {}.subject_groups sg ON sg.id = h.subject_group_id
                JOIN public.subjects s ON s.code = sg.subject_code
                WHERE hs.student_id = $1 
                    AND h.created_at >= $2
                    AND hs.status = 'Done'
                GROUP BY DATE_TRUNC('month', h.created_at), sg.subject_code, s.name
            ),
            monthly_overall AS (
                SELECT 
                    month_date,
                    AVG(avg_score) as overall_score,
                    jsonb_object_agg(subject_code, ROUND(avg_score)) as subject_scores
                FROM subject_monthly_scores
                GROUP BY month_date
            ),
            monthly_with_trends AS (
                SELECT 
                    month_date,
                    overall_score,
                    subject_scores,
                    LAG(overall_score) OVER (ORDER BY month_date) as prev_overall_score
                FROM monthly_overall
            )
            SELECT 
                TO_CHAR(month_date, 'YYYY-MM-DD') as date,
                overall_score,
                subject_scores,
                CASE 
                    WHEN prev_overall_score IS NOT NULL AND overall_score >= prev_overall_score + 10
                    THEN '成绩突破' || ROUND(overall_score) || '分'
                    ELSE NULL
                END as milestone_achieved
            FROM monthly_with_trends
            WHERE overall_score IS NOT NULL
            ORDER BY month_date DESC
            LIMIT 10
            "#,
            tenant_schema, tenant_schema, tenant_schema
        );

        let results = sqlx::query_as::<_, ProgressTrack>(&sql)
            .bind(student_id)
            .bind(time_filter)
            .fetch_all(pool)
            .await?;

        Ok(results)
    }

    // Helper methods
    fn get_time_filter(time_range: Option<&str>) -> DateTime<Utc> {
        let days = Self::get_days_from_range(time_range);
        Utc::now() - Duration::days(days)
    }

    fn get_days_from_range(time_range: Option<&str>) -> i64 {
        match time_range {
            Some("30d") => 30,
            Some("90d") => 90,
            Some("180d") => 180,
            Some("1y") => 365,
            _ => 90, // default to 90 days
        }
    }

    fn generate_recommendation_content(subject: &str, avg_score: f64) -> (String, String, Vec<StudyResource>) {
        match subject {
            "数学" => (
                if avg_score < 60.0 { "加强数学基础练习".to_string() } else { "提升数学解题技巧".to_string() },
                if avg_score < 60.0 {
                    "您在数学基础知识方面需要加强，建议重点复习基础概念和公式".to_string() 
                } else { 
                    "建议通过更多练习来提高数学解题速度和准确性".to_string() 
                },
                vec![
                    StudyResource {
                        resource_type: "video".to_string(),
                        title: "数学基础知识详解".to_string(),
                        url: "#".to_string(),
                        duration: 30,
                    },
                    StudyResource {
                        resource_type: "exercise".to_string(),
                        title: "数学基础练习题".to_string(),
                        url: "#".to_string(),
                        duration: 30,
                    },
                ]
            ),
            "语文" => (
                "提升语文阅读理解能力".to_string(),
                "建议多读优秀文章，提高阅读理解和写作能力".to_string(),
                vec![
                    StudyResource {
                        resource_type: "reading".to_string(),
                        title: "现代文阅读技巧".to_string(),
                        url: "#".to_string(),
                        duration: 20,
                    },
                    StudyResource {
                        resource_type: "exercise".to_string(),
                        title: "阅读理解练习".to_string(),
                        url: "#".to_string(),
                        duration: 25,
                    },
                ]
            ),
            "英语" => (
                "英语语法和词汇强化".to_string(),
                "建议加强英语语法学习和词汇积累".to_string(),
                vec![
                    StudyResource {
                        resource_type: "exercise".to_string(),
                        title: "英语语法练习".to_string(),
                        url: "#".to_string(),
                        duration: 25,
                    },
                ]
            ),
            "物理" => (
                "物理概念理解加强".to_string(),
                "建议重点理解物理基本概念和定律".to_string(),
                vec![
                    StudyResource {
                        resource_type: "video".to_string(),
                        title: "物理基础概念详解".to_string(),
                        url: "#".to_string(),
                        duration: 30,
                    },
                    StudyResource {
                        resource_type: "exercise".to_string(),
                        title: "物理基础练习题".to_string(),
                        url: "#".to_string(),
                        duration: 30,
                    },
                ]
            ),
            _ => (
                format!("{}学科能力提升", subject),
                format!("建议加强{}学科的学习和练习", subject),
                vec![
                    StudyResource {
                        resource_type: "exercise".to_string(),
                        title: format!("{}练习题", subject),
                        url: "#".to_string(),
                        duration: 30,
                    },
                ]
            )
        }
    }
}
