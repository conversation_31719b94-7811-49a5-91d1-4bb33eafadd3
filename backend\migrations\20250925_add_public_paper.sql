CREATE TABLE IF NOT EXISTS "public"."public_paper" (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    paper_name text NOT NULL,
    year int2 NOT NULL,
    "subject_code" varchar(20) NOT NULL,
    grade_level_code varchar(20),
    area_codes varchar(20)[],
    organize_code varchar(20),
    paper_type_code varchar(20),
    exam_project_code varchar(20),
    section_id uuid NOT NULL,
    origin_id bigint,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_deleted bool NOT NULL DEFAULT false
);
COMMENT ON COLUMN "public"."public_paper".subject_code IS '学科';
COMMENT ON COLUMN "public"."public_paper".grade_level_code IS '年级';
COMMENT ON COLUMN "public"."public_paper".area_codes IS '区域编码';
COMMENT ON COLUMN "public"."public_paper".organize_code IS '组织类型';
COMMENT ON COLUMN "public"."public_paper".paper_type_code IS '考试类型';
COMMENT ON COLUMN "public"."public_paper".exam_project_code IS '考试项目';
COMMENT ON COLUMN "public"."public_paper".section_id IS '内容块ID';
COMMENT ON COLUMN "public"."public_paper".origin_id IS '源ID';
COMMENT ON COLUMN "public"."public_paper".updated_at IS '更新时间';