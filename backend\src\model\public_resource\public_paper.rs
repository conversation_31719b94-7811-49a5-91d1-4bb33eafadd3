use chrono::{DateTime, Utc};
use uuid::Uuid;

pub struct PublicPaper {
    pub id: Uuid,
    pub paper_name: String,
    // 统考/校考/联考/练习
    // 高考/中考/高职高考/
    pub area_codes: Option<Vec<String>>, // 区域编码 如：4401
    pub organize_code: Option<String>, // 组织类型：统考/校考/联考/练习
    pub paper_type_code: Option<String>, // 考试类型：模拟/真题/同步练习/复习（一轮、二轮）/月考/期中/期末/竞赛
    pub exam_project_code: Option<String>, // 考试项目：广东新高考/广州中考/数学华杯
    pub subject_code: String, // 学科
    pub grade_level_code: Option<String>, // 年级
    pub year: Option<i16>, // 出版年份
    pub updated_at: DateTime<Utc>,
    pub section_id: Uuid, // 小节内容ID
    pub origin_id: Option<i64>, // 来源id
    pub is_deleted: bool,
}