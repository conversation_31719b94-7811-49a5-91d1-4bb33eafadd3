use serde::{Deserialize, Serialize};
use serde_json::Value;
use sqlx::{FromRow, PgPool, QueryBuilder};
use uuid::Uuid;

/**
 * 作者：张瀚
 * 说明：公共域答题卡接口
 */
pub struct AnswerSheetRepository {}

impl AnswerSheetRepository {
    /**
     * 作者：张瀚
     * 说明：新建答题卡,已存在会报错
     */
    pub async fn insert(db: &PgPool, params: InsertParams) -> Result<AnswerSheetModel, String> {
        let mut builder = QueryBuilder::new("INSERT INTO public.answer_sheets (data_version,render_data,annotation) ");
        builder
            .push_values(vec![params], |mut b, a| {
                b.push_bind(a.data_version).push_bind(a.render_data).push_bind(a.annotation);
            })
            .push(" RETURNING * ")
            .build_query_as()
            .fetch_one(db)
            .await
            .map_err(|e| e.to_string())
    }
}

#[derive(Debug, Serialize, Deserialize, <PERSON>lone, FromRow)]
#[serde(rename_all = "camelCase")]
pub struct InsertParams {
    pub data_version: String,
    ///渲染数据,根据版本,1.0.0版本使用的是RenderData
    pub render_data: Value,
    ///渲染数据,根据版本,1.0.0版本使用的是Annotation
    pub annotation: Option<Value>,
}

/**
 * 作者：张瀚
 * 说明：数据库中使用的答题卡映射对象
 */
#[derive(Debug, Serialize, Deserialize, Clone, FromRow)]
#[serde(rename_all = "camelCase")]
pub struct AnswerSheetModel {
    pub id: Uuid,
    pub data_version: String,
    ///渲染数据,根据版本,1.0.0版本使用的是RenderData
    pub render_data: Value,
    ///渲染数据,根据版本,1.0.0版本使用的是Annotation
    pub annotation: Option<Value>,
}
