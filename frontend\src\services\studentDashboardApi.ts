import { ApiResponse } from '@/types';
import apiClient from './apiClient';

// 学生成绩看板相关类型定义
export interface StudentScoreOverview {
    total_exams: number;
    average_score: number;
    class_rank: number;
    grade_rank: number;
    improvement_trend: 'up' | 'down' | 'stable';
    improvement_rate: number;
    homework_completion_rate: number;
    total_homework_assigned: number;
    total_homework_completed: number;
}

export interface RecentExamScore {
    id: string;
    exam_id: string;
    exam_name: string;
    subject_id: string;
    subject_name: string;
    score: number;
    total_possible: number;
    percentage: number;
    class_rank: number;
    grade_rank: number;
    exam_date: string;
    exam_type: string;
}

export interface SubjectScoreTrend {
    subject_id: string;
    subject_name: string;
    latest_score: number;
    latest_rank: number;
    trend: 'up' | 'down' | 'stable';
    improvement_rate: number;
    score_history: Array<{
        date: string;
        exam_name: string;
        score: number;
        percentage: number;
        rank: number;
    }>;
}

export interface StudentHomeworkStatus {
    id: string;
    homework_id: string;
    homework_name: string;
    subject_id: string;
    subject_name: string;
    due_date: string;
    submission_date?: string;
    status: 'completed' | 'pending' | 'overdue' | 'graded';
    score?: number;
    total_score?: number;
    percentage?: number;
    feedback?: string;
}

export interface StudentDashboardData {
    overview: StudentScoreOverview;
    recent_scores: RecentExamScore[];
    subject_trends: SubjectScoreTrend[];
    homework_status: StudentHomeworkStatus[];
    performance_insights: {
        strongest_subjects: string[];
        improvement_needed: string[];
        recent_achievements: string[];
        upcoming_deadlines: Array<{
            homework_id: string;
            homework_name: string;
            subject_name: string;
            due_date: string;
            days_remaining: number;
        }>;
    };
}

export interface StudentScoreAnalytics {
    subject_performance: Array<{
        subject_name: string;
        average_score: number;
        best_score: number;
        worst_score: number;
        trend: 'improving' | 'declining' | 'stable';
        knowledge_points: Array<{
            point_name: string;
            mastery_level: number;
            improvement_suggestion: string;
        }>;
    }>;
    exam_performance_history: Array<{
        exam_date: string;
        exam_name: string;
        total_score: number;
        class_rank: number;
        grade_rank: number;
    }>;
    homework_analytics: {
        completion_rate: number;
        average_score: number;
        on_time_submission_rate: number;
        subject_breakdown: Array<{
            subject_name: string;
            completion_rate: number;
            average_score: number;
        }>;
    };
}

export const studentDashboardApi = {
    /**
     * 获取学生成绩看板完整数据
     */
    getDashboardData: async (tenant_name: string): Promise<ApiResponse<StudentDashboardData>> => {
        return apiClient.get(`/api/v1/tenants/${tenant_name}/student/dashboard`);
    },

    /**
     * 获取学生成绩概览
     */
    getScoreOverview: async (tenant_name: string): Promise<ApiResponse<StudentScoreOverview>> => {
        return apiClient.get(`/api/v1/tenants/${tenant_name}/student/scores/overview`);
    },

    /**
     * 获取最近考试成绩
     */
    getRecentExamScores: async (
        tenant_name: string,
        params?: {
            limit?: number;
            subject_id?: string;
            exam_type?: string;
        }
    ): Promise<ApiResponse<RecentExamScore[]>> => {
        const queryParams = new URLSearchParams();
        if (params?.limit) queryParams.append('limit', params.limit.toString());
        if (params?.subject_id) queryParams.append('subject_id', params.subject_id);
        if (params?.exam_type) queryParams.append('exam_type', params.exam_type);

        const url = `/api/v1/tenants/${tenant_name}/student/scores/recent${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
        return apiClient.get(url);
    },

    /**
     * 获取学科成绩趋势
     */
    getSubjectTrends: async (
        tenant_name: string,
        params?: {
            subject_ids?: string[];
            months?: number; // 获取最近几个月的数据
        }
    ): Promise<ApiResponse<SubjectScoreTrend[]>> => {
        return apiClient.post(`/api/v1/tenants/${tenant_name}/student/scores/trends`, params || {});
    },

    /**
     * 获取作业状态
     */
    getHomeworkStatus: async (
        tenant_name: string,
        params?: {
            status?: 'all' | 'pending' | 'completed' | 'overdue';
            limit?: number;
            subject_id?: string;
        }
    ): Promise<ApiResponse<StudentHomeworkStatus[]>> => {
        const queryParams = new URLSearchParams();
        if (params?.status && params.status !== 'all') queryParams.append('status', params.status);
        if (params?.limit) queryParams.append('limit', params.limit.toString());
        if (params?.subject_id) queryParams.append('subject_id', params.subject_id);

        const url = `/api/v1/tenants/${tenant_name}/student/homework/status${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
        return apiClient.get(url);
    },

    /**
     * 获取学生成绩详细分析
     */
    getScoreAnalytics: async (
        tenant_name: string,
        params?: {
            time_range?: '1month' | '3months' | '6months' | '1year';
            include_knowledge_points?: boolean;
        }
    ): Promise<ApiResponse<StudentScoreAnalytics>> => {
        return apiClient.post(`/api/v1/tenants/${tenant_name}/student/scores/analytics`, params || {});
    },

    /**
     * 获取学生在特定考试中的详细成绩
     */
    getExamScoreDetail: async (
        tenant_name: string,
        exam_id: string
    ): Promise<ApiResponse<{
        exam_info: {
            id: string;
            name: string;
            date: string;
            type: string;
        };
        total_score: number;
        total_possible: number;
        percentage: number;
        class_rank: number;
        grade_rank: number;
        subject_scores: Array<{
            subject_name: string;
            score: number;
            total_possible: number;
            percentage: number;
            rank: number;
            question_analysis: Array<{
                question_number: string;
                score: number;
                total_possible: number;
                correct: boolean;
                knowledge_points: string[];
            }>;
        }>;
    }>> => {
        return apiClient.get(`/api/v1/tenants/${tenant_name}/student/exams/${exam_id}/score-detail`);
    },

    /**
     * 获取作业详细信息
     */
    getHomeworkDetail: async (
        tenant_name: string,
        homework_id: string
    ): Promise<ApiResponse<{
        homework_info: {
            id: string;
            name: string;
            subject_name: string;
            due_date: string;
            description: string;
        };
        submission_status: 'not_submitted' | 'submitted' | 'graded';
        submission_date?: string;
        score?: number;
        total_score?: number;
        percentage?: number;
        feedback?: string;
        teacher_comments?: string;
        question_scores?: Array<{
            question_id: string;
            question_number: string;
            score: number;
            total_possible: number;
            feedback: string;
        }>;
    }>> => {
        return apiClient.get(`/api/v1/tenants/${tenant_name}/student/homework/${homework_id}/detail`);
    },

    /**
     * 获取学习建议
     */
    getStudyRecommendations: async (
        tenant_name: string
    ): Promise<ApiResponse<{
        overall_performance: string;
        subject_recommendations: Array<{
            subject_name: string;
            current_level: string;
            target_level: string;
            specific_suggestions: string[];
            recommended_resources: Array<{
                type: 'exercise' | 'video' | 'article';
                title: string;
                url: string;
                difficulty: 'easy' | 'medium' | 'hard';
            }>;
        }>;
        study_plan: Array<{
            week: number;
            focus_subjects: string[];
            daily_goals: string[];
            practice_suggestions: string[];
        }>;
    }>> => {
        return apiClient.get(`/api/v1/tenants/${tenant_name}/student/recommendations`);
    },

    /**
     * 更新学生查看成绩报告的时间记录
     */
    updateReportViewTime: async (
        tenant_name: string,
        params: {
            report_type: 'exam' | 'homework' | 'dashboard';
            report_id?: string;
            view_duration: number; // 秒
        }
    ): Promise<ApiResponse<void>> => {
        return apiClient.post(`/api/v1/tenants/${tenant_name}/student/report-view-time`, params);
    }
};

export default studentDashboardApi; 