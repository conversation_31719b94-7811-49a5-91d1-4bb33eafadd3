import apiClient from './apiClient';

// 知识点掌握情况
export interface KnowledgePointMastery {
    point_name: string;
    subject: string;
    mastery_level: number; // 0-100
    recent_practice_count: number;
    improvement_trend: 'up' | 'down' | 'stable';
    difficulty_level: 'easy' | 'medium' | 'hard';
    recommended_practice_time: number; // 分钟
}

// 学科能力雷达图数据
export interface SubjectAbility {
    subject: string;
    understanding: number; // 理解能力
    application: number;   // 应用能力
    analysis: number;      // 分析能力
    synthesis: number;     // 综合能力
    memory: number;        // 记忆能力
}

// 学习建议资源
export interface StudyResource {
    type: 'video' | 'exercise' | 'reading';
    title: string;
    url: string;
    duration: number;
}

// 学习建议
export interface StudyRecommendation {
    id: string;
    type: 'urgent' | 'important' | 'suggested';
    subject: string;
    title: string;
    description: string;
    estimated_time: number; // 分钟
    difficulty: 'easy' | 'medium' | 'hard';
    priority: number; // 1-5
    resources: StudyResource[];
}

// 进步轨迹
export interface ProgressTrack {
    date: string;
    overall_score: number;
    subject_scores: Record<string, number>;
    milestone_achieved?: string;
    notes?: string;
}

// 查询参数
export interface StudentPerformanceQuery {
    time_range?: '30d' | '90d' | '180d' | '1y';
    subject?: string;
}

// 完整的学生表现分析数据
export interface StudentPerformanceAnalysis {
    knowledge_points: KnowledgePointMastery[];
    subject_abilities: SubjectAbility[];
    recommendations: StudyRecommendation[];
    progress_tracks: ProgressTrack[];
}

// API响应类型
interface ApiResponse<T> {
    success: boolean;
    code: number;
    message: string;
    data: T;
}

export const StudentPerformanceApi = {
    /**
     * 获取当前登录学生的知识点掌握情况
     */
    async getKnowledgePointMastery(params?: StudentPerformanceQuery): Promise<KnowledgePointMastery[]> {
        const response = await apiClient.get<ApiResponse<KnowledgePointMastery[]>>(
            '/api/v1/performance/knowledge-points',
            { params }
        );
        return response.data.data;
    },

    /**
     * 获取当前登录学生的学科能力雷达图数据
     */
    async getSubjectAbilities(params?: StudentPerformanceQuery): Promise<SubjectAbility[]> {
        const response = await apiClient.get<ApiResponse<SubjectAbility[]>>(
            '/api/v1/performance/subject-abilities',
            { params }
        );
        return response.data.data;
    },

    /**
     * 获取当前登录学生的学习建议
     */
    async getStudyRecommendations(params?: StudentPerformanceQuery): Promise<StudyRecommendation[]> {
        const response = await apiClient.get<ApiResponse<StudyRecommendation[]>>(
            '/api/v1/performance/recommendations',
            { params }
        );
        return response.data.data;
    },

    /**
     * 获取当前登录学生的学习进步轨迹
     */
    async getProgressTracks(params?: StudentPerformanceQuery): Promise<ProgressTrack[]> {
        const response = await apiClient.get<ApiResponse<ProgressTrack[]>>(
            '/api/v1/performance/progress-tracks',
            { params }
        );
        return response.data.data;
    },

    /**
     * 获取当前登录学生的完整表现分析
     */
    async getCompletePerformanceAnalysis(params?: StudentPerformanceQuery): Promise<StudentPerformanceAnalysis> {
        const response = await apiClient.get<ApiResponse<StudentPerformanceAnalysis>>(
            '/api/v1/performance/complete-analysis',
            { params }
        );
        return response.data.data;
    },

    // 管理员/教师访问指定学生的API方法
    /**
     * 获取指定学生的知识点掌握情况（管理员/教师使用）
     */
    async getStudentKnowledgePointMastery(
        studentId: string, 
        params?: StudentPerformanceQuery
    ): Promise<KnowledgePointMastery[]> {
        const response = await apiClient.get<ApiResponse<KnowledgePointMastery[]>>(
            `/api/v1/performance/students/${studentId}/knowledge-points`,
            { params }
        );
        return response.data.data;
    },

    /**
     * 获取指定学生的学科能力数据（管理员/教师使用）
     */
    async getStudentSubjectAbilities(
        studentId: string, 
        params?: StudentPerformanceQuery
    ): Promise<SubjectAbility[]> {
        const response = await apiClient.get<ApiResponse<SubjectAbility[]>>(
            `/api/v1/performance/students/${studentId}/subject-abilities`,
            { params }
        );
        return response.data.data;
    },

    /**
     * 获取指定学生的学习建议（管理员/教师使用）
     */
    async getStudentStudyRecommendations(
        studentId: string, 
        params?: StudentPerformanceQuery
    ): Promise<StudyRecommendation[]> {
        const response = await apiClient.get<ApiResponse<StudyRecommendation[]>>(
            `/api/v1/performance/students/${studentId}/recommendations`,
            { params }
        );
        return response.data.data;
    },

    /**
     * 获取指定学生的进步轨迹（管理员/教师使用）
     */
    async getStudentProgressTracks(
        studentId: string, 
        params?: StudentPerformanceQuery
    ): Promise<ProgressTrack[]> {
        const response = await apiClient.get<ApiResponse<ProgressTrack[]>>(
            `/api/v1/performance/students/${studentId}/progress-tracks`,
            { params }
        );
        return response.data.data;
    },

    /**
     * 获取指定学生的完整表现分析（管理员/教师使用）
     */
    async getStudentCompletePerformanceAnalysis(
        studentId: string, 
        params?: StudentPerformanceQuery
    ): Promise<StudentPerformanceAnalysis> {
        const response = await apiClient.get<ApiResponse<StudentPerformanceAnalysis>>(
            `/api/v1/performance/students/${studentId}/complete-analysis`,
            { params }
        );
        return response.data.data;
    },
};
