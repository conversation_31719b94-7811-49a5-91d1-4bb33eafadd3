import { usePageFullWidth } from '@/hooks/useLayoutWidth'
import React from 'react'
import { useParams } from 'react-router-dom'
import './AnswerSheetEditing.scss'
import { PublicAnswerSheet } from './components/public-answer-sheet/PublicAnswerSheet'
import { PublicSheet } from './components/public-sheet/PublicSheet'
import { TenantSheet } from './components/tenant-sheet/TenantSheet'

/**
 * 作者：张瀚
 * 说明：答题卡编辑主页面，根据版本不同切换不同的渲染逻辑
 */
const AnswerSheetEditing: React.FC = () => {
  usePageFullWidth()
  const { id = '', schema_name = '' } = useParams<{ schema_name: string; id: string }>()
  switch (schema_name) {
    case 'public':
      //老版本的教辅跳转的
      return <PublicSheet answerId={id}></PublicSheet>
    case 'public_answer_sheet':
      //新版的公共域答题卡
      return <PublicAnswerSheet answerSheetId={id}></PublicAnswerSheet>
    case 'tenant_answer_sheet':
      //新版的租户域答题卡
      return
    default:
      //老版本的租户内的试卷跳转
      return <TenantSheet paperId={id} schemaName={schema_name}></TenantSheet>
  }
}
export default AnswerSheetEditing
