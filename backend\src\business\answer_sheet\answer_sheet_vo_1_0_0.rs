use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use std::collections::HashMap;
/**
 * 作者：张瀚
 * 说明：渲染配置
 */
#[derive(Debug, Serialize, Deserialize, Clone, FromRow)]
#[serde(rename_all = "camelCase")]
pub struct RenderData {
    pub dpi: usize,
    pub width: f64,
    pub height: f64,
    ///页面内边距,上右下左四个数字,单位像素
    pub padding: Vec<f64>,
    ///页面是否竖向显示
    pub page_orientation_is_vertical: bool,
    ///分栏数量
    pub bucket_len: usize,
    ///是否显示页码
    pub show_page_index: bool,
    ///是否显示定位点,定位点位于页面内部四个顶点，上面两个是横向，下面两个竖向
    pub show_pos_point: bool,
    ///定位点宽度，指左上角那个
    pub pos_point_width: f64,
    ///定位点高度度，指左上角那个
    pub pos_point_height: f64,
    ///总页码
    pub page_total: usize,
    ///标题栏，标题、考生姓名、二维码等等
    pub title_bar: TitleBar,
    ///每个答题区自己的配置文件
    pub answer_area_id_to_config_map: HashMap<String, AnswerAreaConfig>,
}

#[derive(Debug, Serialize, Deserialize, Clone, FromRow)]
#[serde(rename_all = "camelCase")]
pub struct TitleBar {
    ///考试名称
    pub exam_name: String,
    /**
     * 模板名称
     */
    pub model_name: String,
    /**
     * 学号长度
     */
    pub student_number_len: usize,
    /**
     * 是否显示二维码
     */
    pub show_q_r_code: bool,
    /**
     * 准考证号是否需要涂卡
     */
    pub need_paint_card: bool,
    /**
     * 学号处文本信息
     */
    pub fill_text: Option<String>,
    /**
     * 涂卡区域和准考证是不是横向显示
     */
    pub is_horizontal: bool,
    pub paper_id: Option<String>,
    pub card_id: Option<String>,
    pub student_number: Option<String>,
    pub student_name: Option<String>,
    pub student_class_name: Option<String>,
}

/**
 * 作者：张瀚
 * 说明：答题区的渲染配置，根据类型不同使用不同的字段
 */
#[derive(Debug, Serialize, Deserialize, Clone, FromRow)]
#[serde(rename_all = "camelCase")]
pub struct AnswerAreaConfig {
    pub area_type: AnswerAreaValueKeyEnum,
    pub inline_config: Option<InlineConfig>,
    pub multiline_config: Option<MultilineConfig>,
    pub choices_config: Option<ChoiceConfig>,
    pub composition_config: Option<CompositionConfig>,
    pub image_location_config: Option<ImageLocationConfig>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub enum AnswerAreaValueKeyEnum {
    Choices,
    TrueOrFalse,
    Multiline,
    Inline,
    Composition,
    Wrap,
    ImageLocation,
}

#[derive(Debug, Serialize, Deserialize, Clone, FromRow)]
#[serde(rename_all = "camelCase")]
pub struct InlineConfig {
    ///有多少个字的宽度
    pub content_len: usize,
    ///填空的样式
    pub line_type: InlineConfigLineTypeEnum,
    ///作答区的行高，单位像素，默认32px
    pub line_height: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub enum InlineConfigLineTypeEnum {
    None,
    Underline,
    Block,
}

#[derive(Debug, Serialize, Deserialize, Clone, FromRow)]
#[serde(rename_all = "camelCase")]
pub struct MultilineConfig {
    ///行数
    pub line_len: usize,
    ///填空的样式
    pub line_type: MultilineConfigLineTypeEnum,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub enum MultilineConfigLineTypeEnum {
    None,
    Underline,
    Block,
}

#[derive(Debug, Serialize, Deserialize, Clone, FromRow)]
#[serde(rename_all = "camelCase")]
pub struct ChoiceConfig {
    ///是否横向显示选项
    pub is_horizontal: bool,
}

#[derive(Debug, Serialize, Deserialize, Clone, FromRow)]
#[serde(rename_all = "camelCase")]
pub struct CompositionConfig {
    ///字数
    pub content_len: usize,
}

#[derive(Debug, Serialize, Deserialize, Clone, FromRow)]
#[serde(rename_all = "camelCase")]
pub struct ImageLocationConfig {
    pub width: f64,
    pub height: f64,
    pub x: f64,
    pub y: f64,
}

#[derive(Debug, Serialize, Deserialize, Clone, FromRow)]
#[serde(rename_all = "camelCase")]
pub struct Annotation {
    pub dpi: f64,
    pub width: f64,
    pub height: f64,
    pub anchorAnnotation: AnchorAnnotation,
    pub pageAnnotation: PageAnnotation,
    pub blockAnnotations: Vec<BlockAnnotation>,
}

#[derive(Debug, Serialize, Deserialize, Clone, FromRow)]
#[serde(rename_all = "camelCase")]
pub struct AnchorAnnotation {
    pub key: AnchorAnnotationKeyEnum,
    pub value: FourPointAnchor,
}
#[derive(Debug, Serialize, Deserialize, Clone, FromRow)]
#[serde(rename_all = "camelCase")]
pub struct PageAnnotation {
    pub key: PageAnnotationKeyEnum,
    pub value: Vec<BlockUnit>,
}
#[derive(Debug, Serialize, Deserialize, Clone, FromRow)]
#[serde(rename_all = "camelCase")]
pub struct BlockAnnotation {
    pub id: String,
    pub blockType: BlockTypeEnum,
    pub mode: BlockAnnotationMode,
    pub pageNumber: f64,
    pub bucketNumber: f64,
    pub rects: Vec<BlockUnit>,
}
#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub enum BlockTypeEnum {
    Number,
    CardAndStudentNumber,
    FillChoice,
    FillTrueOrFalse,
    HandwriteQuestion,
}
#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub enum BlockAnnotationMode {
    NumberMode(NumberMode),
    CardAndStudentNumberMode(CardAndStudentNumberMode),
    FillMode(FillMode),
    HandwriteQuestion(HandwriteQuestion),
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub enum NumberMode {
    Ocr,
    // FillBlock , // 填涂 参数类型待定（方向，学号位数）
}
#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub enum CardAndStudentNumberMode {
    QrCode, // barcode, ocr
}
#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub enum FillMode {
    // 类型为选择题、判断题的模式
    FillRandom,
    // FillBlock = 'fillBlock' // 多个题形成区块，需要额外参数
}
#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub enum HandwriteQuestion {
    AnswerArea,
    // HandwriteScore , // 扩展手写打分区
    // StrikethroughScore = 'strikethroughScore' // 扩展划线打分模式
}
#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub enum AnchorAnnotationKeyEnum {
    // ImageFeat = 'ImageFeat' // 图像特征定位
    FourPoint,
}
#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub enum PageAnnotationKeyEnum {
    BinaryFillRandom,
}
#[derive(Debug, Serialize, Deserialize, Clone, FromRow)]
#[serde(rename_all = "camelCase")]
pub struct FourPointAnchor {
    pub width: f64,
    pub height: f64,
    pub left: f64,
    pub right: f64,
    pub top: f64,
    pub bottom: f64,
}
#[derive(Debug, Serialize, Deserialize, Clone, FromRow)]
#[serde(rename_all = "camelCase")]
pub struct BlockUnit {
    pub x: f64,
    pub y: f64,
    pub w: f64,
    pub h: f64,
}
