import React, { useState } from 'react'
import { SectionDetail, QuestionGroupData } from '@/types/teachingAid.ts'
import { Switch } from '@/components/ui/switch'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger } from '@/components/ui/sheet'
import { Eye } from 'lucide-react'
import QuestionItem from './QuestionItem'
import Mathdown from '@/components/question-card/v0.0.0/mathdown/Mathdown'

interface SectionRendererProps {
  section: SectionDetail
}

const SectionRenderer: React.FC<SectionRendererProps> = ({ section }) => {
  const [previewOpen, setPreviewOpen] = useState(false)
  const [showAnswers, setShowAnswers] = useState(false)

  // 渲染section的items
  const renderSectionItems = () => {
    if (!section?.items) return null

    return section.items.map((item, index) => {
      if (item.key === 'text') {
        return (
          <div key={index} className='mb-4'>
            <Mathdown content={item.value} />
          </div>
        )
      } else if (item.key === 'questionGroup') {
        const questionGroup = item.value as QuestionGroupData
        return (
          <div key={index} className='mb-6'>
            {/* 题组文本 */}
            {questionGroup.text && (
              <div className='mb-4 p-3 bg-gray-50 border-l-4 border-blue-500'>
                <Mathdown content={questionGroup.text} />
              </div>
            )}

            {/* 题目列表 */}
            {questionGroup.questions &&
              questionGroup.questions.map((questionDetail) => (
                <QuestionItem
                  key={questionDetail.questionId}
                  question={questionDetail.question}
                  questionNumber={questionDetail.questionNumber}
                  answerAreaDetails={questionDetail.answerAreaDetails || []}
                  showAnswers={showAnswers}
                />
              ))}
          </div>
        )
      }
      return null
    })
  }

  // 渲染试卷预览抽屉内容
  const renderPreviewContent = () => {
    if (!section?.snapshots || section.snapshots.length === 0) {
      return <div className='p-4 text-center text-gray-500'>暂无试卷预览图片</div>
    }

    return (
      <div className='p-4 space-y-4'>
        {section.snapshots.map((snapshot, index) => (
          <div key={index} className='border rounded-lg overflow-hidden'>
            <img src={snapshot} alt={`试卷预览 ${index + 1}`} className='w-full h-auto object-contain' loading='lazy' />
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className='relative'>
      {/* 顶部控制栏 */}
      <div className='sticky top-0 z-10 bg-white border-b p-4 mb-4 flex items-center justify-between shadow-sm'>
        <h2 className='text-lg font-semibold'>教辅内容</h2>

        <div className='flex items-center gap-4'>
          {/* 答案显示开关 */}
          <div className='flex items-center gap-2'>
            <span className='text-sm text-gray-600'>显示答案</span>
            <Switch checked={showAnswers} onCheckedChange={setShowAnswers} />
          </div>

          {/* 试卷预览按钮 */}
          {section?.snapshots && section.snapshots.length > 0 && (
            <Sheet open={previewOpen} onOpenChange={setPreviewOpen}>
              <SheetTrigger asChild>
                <Button variant='outline' size='sm' className='flex items-center gap-2'>
                  <Eye className='w-4 h-4' />
                  预览快照
                </Button>
              </SheetTrigger>
              <SheetContent side='right' className='h-full overflow-y-auto w-[800px] sm:max-w-[800px]'>
                <SheetHeader>
                  <SheetTitle>快照预览</SheetTitle>
                </SheetHeader>
                <div className='mt-4'>{renderPreviewContent()}</div>
              </SheetContent>
            </Sheet>
          )}
        </div>
      </div>

      {/* 内容区域 */}
      <div className='px-4'>{section ? renderSectionItems() : <div className='text-center text-gray-500 py-8'>本章节无内容</div>}</div>
    </div>
  )
}

export default SectionRenderer
