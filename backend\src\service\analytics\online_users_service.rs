use anyhow::Result;
use sqlx::PgPool;
use uuid::Uuid;

use crate::model::analytics::online_users_model::*;
use crate::repository::analytics::online_users_repository::OnlineUsersRepository;
use crate::middleware::auth_middleware::AuthContext;

/// 在线用户管理服务
pub struct OnlineUsersService {
    repository: OnlineUsersRepository,
}

impl OnlineUsersService {
    pub fn new(pool: PgPool) -> Self {
        Self {
            repository: OnlineUsersRepository::new(pool),
        }
    }

    /// 更新用户在线状态
    pub async fn update_online_status(&self, request: UpsertOnlineUserRequest) -> Result<()> {
        self.repository.upsert_online_user(request).await
    }

    /// 用户登出
    pub async fn logout_user(&self, user_id: Uuid, session_id: Uuid) -> Result<()> {
        self.repository.logout_user(user_id, session_id).await
    }

    /// 获取当前在线用户数量
    pub async fn get_online_count(&self, tenant_id: Option<Uuid>, _context: &AuthContext) -> Result<i64> {
        // 根据用户权限过滤租户
        let authorized_tenant_id = self.get_authorized_tenant_id(tenant_id, _context).await?;
        
        self.repository.get_current_online_count(authorized_tenant_id).await
    }

    /// 获取在线用户列表
    pub async fn get_online_users(
        &self,
        params: &OnlineUserQueryParams,
        context: &AuthContext,
    ) -> Result<Vec<OnlineUserActivity>> {
        // 应用权限过滤
        let _filtered_params = self.apply_permission_filter(params, context).await?;
        Ok(vec![])
    }

    /// 清理过期用户
    pub async fn cleanup_inactive_users(&self, inactive_minutes: i32) -> Result<u64> {
        self.repository.cleanup_stale_users(inactive_minutes).await
    }

    /// 获取在线用户统计
    pub async fn get_online_stats(&self, tenant_id: Option<Uuid>, context: &AuthContext) -> Result<OnlineUserStats> {
        let authorized_tenant_id = self.get_authorized_tenant_id(tenant_id, context).await?;
        self.repository.get_online_stats(authorized_tenant_id).await
    }

    /// 根据权限过滤参数
    async fn apply_permission_filter(
        &self,
        params: &OnlineUserQueryParams,
        context: &AuthContext,
    ) -> Result<OnlineUserQueryParams> {
        let mut filtered_params = params.clone();
        
        // 非超级管理员只能查看自己租户的数据
        if !self.is_super_admin(context).await? {
            filtered_params.tenant_id = context.tenant_links.first().map(|link| link.tenant_id);
        }
        
        Ok(filtered_params)
    }

    /// 获取用户有权限查看的租户ID
    async fn get_authorized_tenant_id(
        &self,
        requested_tenant_id: Option<Uuid>,
        context: &AuthContext,
    ) -> Result<Option<Uuid>> {
        if self.is_super_admin(context).await? {
            Ok(requested_tenant_id)
        } else {
            if let Some(requested_tenant_id) = requested_tenant_id {
                // 检查权限
                if context.tenant_links.iter().any(|link| link.tenant_id == requested_tenant_id) {
                    return Ok(Some(requested_tenant_id));
                }
            }
            Ok(None)
        }
    }

    /// 检查是否为超级管理员
    async fn is_super_admin(&self, context: &AuthContext) -> Result<bool> {
        Ok(context.is_super_admin())
    }
} 