use serde::{Deserialize, Serialize};
use sqlx::{FromRow, PgPool, QueryBuilder};
use uuid::Uuid;

/**
 * 作者：张瀚
 * 说明：公共域内容块和答题卡关联接口
 */
pub struct SectionAnswerSheetRepository {}

impl SectionAnswerSheetRepository {
    /**
     * 作者：张瀚
     * 说明：查询内容关联的答题卡ID列表
     */
    pub async fn find_by_section_id(db: &PgPool, section_id: &Uuid) -> Result<Vec<Uuid>, String> {
        let mut builder = QueryBuilder::new("SELECT sas.answer_sheet_id FROM public.section_answer_sheet sas WHERE sas.section_id = ");
        builder.push_bind(section_id).build_query_scalar().fetch_all(db).await.map_err(|e| e.to_string())
    }

    /**
     * 作者：张瀚
     * 说明：绑定内容块和答题卡
     */
    pub async fn bind_section_and_answer_sheet(db: &PgPool, section_id: &Uuid, answer_sheet_id: &Uuid) -> Result<(), String> {
        let mut builder = QueryBuilder::new("INSERT INTO public.section_answer_sheet (section_id,answer_sheet_id) ");
        builder
            .push_values(vec![vec![section_id, answer_sheet_id]], |mut b, a| {
                b.push_bind(a[0]).push_bind(a[1]);
            })
            .build()
            .execute(db)
            .await
            .map_err(|e| e.to_string())
            .map(|_e| ())
    }
}

#[derive(Debug, Serialize, Deserialize, Clone, FromRow)]
#[serde(rename_all = "camelCase")]
pub struct SectionAnswerSheetModel {
    pub section_id: Uuid,
    pub answer_sheet_id: Uuid,
}
